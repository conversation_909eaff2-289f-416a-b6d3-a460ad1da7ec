#include "usart1_app.h"
#include "cmic_gd32f470vet6.h"
#include <string.h>
#include <stdarg.h>

/* 全局变量定义 */
uint8_t usart1_dma_buffer[USART1_BUFFER_SIZE];
uint8_t usart1_rx_buffer[USART1_BUFFER_SIZE];
volatile uint8_t usart1_rx_flag = 0;
volatile usart1_state_t usart1_state = USART1_STATE_IDLE;
usart1_statistics_t usart1_stats;

/* 私有变量 */
static char usart1_cmd_buffer[USART1_CMD_BUFFER_SIZE];
static uint16_t usart1_cmd_index = 0;
static uint8_t usart1_echo_enabled = 1;
static uint16_t usart1_received_length = 0;

/*!
    \brief      初始化USART1应用层
    \param[in]  none
    \param[out] none
    \retval     none
*/
void usart1_app_init(void)
{
    /* 初始化底层硬件 */
    bsp_usart1_init();
    
    /* 初始化状态 */
    usart1_state = USART1_STATE_IDLE;
    usart1_rx_flag = 0;
    
    /* 清零统计信息 */
    memset(&usart1_stats, 0, sizeof(usart1_statistics_t));
    
    /* 清零缓冲区 */
    memset(usart1_dma_buffer, 0, sizeof(usart1_dma_buffer));
    memset(usart1_rx_buffer, 0, sizeof(usart1_rx_buffer));
    memset(usart1_cmd_buffer, 0, sizeof(usart1_cmd_buffer));
    
    usart1_cmd_index = 0;
    usart1_received_length = 0;
    
    /* 发送启动信息 */
    usart1_printf("\r\n=== USART1 RS485 Interface Ready ===\r\n");
    usart1_printf("Baudrate: 115200\r\n");
    usart1_printf("Type 'help' for available commands\r\n");
    usart1_printf("=====================================\r\n");
}

/*!
    \brief      USART1任务函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void usart1_task(void)
{
    /* 检查是否有数据接收 */
    if (usart1_rx_flag) {
        usart1_rx_flag = 0;
        usart1_state = USART1_STATE_RECEIVING;
        
        /* 计算接收长度 */
        usart1_received_length = USART1_BUFFER_SIZE - dma_transfer_number_get(DMA1, DMA_CH5);
        
        if (usart1_received_length > 0) {
            /* 复制数据到处理缓冲区 */
            memcpy(usart1_rx_buffer, usart1_dma_buffer, usart1_received_length);
            
            /* 更新统计信息 */
            usart1_stats.bytes_received += usart1_received_length;
            usart1_stats.frames_received++;
            usart1_stats.last_activity_time = get_system_ms();
            
            /* 处理接收到的数据 */
            usart1_process_received_data(usart1_rx_buffer, usart1_received_length);
        }
        
        /* 重新启动DMA接收 */
        dma_channel_disable(DMA1, DMA_CH5);
        dma_transfer_number_config(DMA1, DMA_CH5, USART1_BUFFER_SIZE);
        dma_channel_enable(DMA1, DMA_CH5);
        
        usart1_state = USART1_STATE_IDLE;
    }
}

/*!
    \brief      处理接收到的数据
    \param[in]  data: 接收到的数据
    \param[in]  length: 数据长度
    \param[out] none
    \retval     none
*/
void usart1_process_received_data(uint8_t* data, uint16_t length)
{
    for (uint16_t i = 0; i < length; i++) {
        char ch = (char)data[i];
        
        /* 回显字符（如果启用） */
        if (usart1_echo_enabled && ch != '\r' && ch != '\n') {
            usart1_send_char(ch);
        }
        
        /* 处理命令 */
        if (ch == '\r' || ch == '\n') {
            if (usart1_cmd_index > 0) {
                usart1_cmd_buffer[usart1_cmd_index] = '\0';
                usart1_send_string("\r\n");
                
                /* 处理命令 */
                usart1_process_command(usart1_cmd_buffer);
                
                /* 重置命令缓冲区 */
                usart1_cmd_index = 0;
                memset(usart1_cmd_buffer, 0, sizeof(usart1_cmd_buffer));
                
                usart1_stats.cmd_processed++;
            }
        } else if (ch == '\b' || ch == 0x7F) {  // 退格键
            if (usart1_cmd_index > 0) {
                usart1_cmd_index--;
                usart1_cmd_buffer[usart1_cmd_index] = '\0';
                if (usart1_echo_enabled) {
                    usart1_send_string("\b \b");  // 退格-空格-退格
                }
            }
        } else if (ch >= 0x20 && ch <= 0x7E) {  // 可打印字符
            if (usart1_cmd_index < USART1_CMD_BUFFER_SIZE - 1) {
                usart1_cmd_buffer[usart1_cmd_index++] = ch;
            }
        }
    }
}

/*!
    \brief      发送单个字符
    \param[in]  ch: 要发送的字符
    \param[out] none
    \retval     none
*/
void usart1_send_char(char ch)
{
    usart_data_transmit(USART1, (uint8_t)ch);
    while (RESET == usart_flag_get(USART1, USART_FLAG_TBE));
    
    usart1_stats.bytes_sent++;
}

/*!
    \brief      发送字符串
    \param[in]  str: 要发送的字符串
    \param[out] none
    \retval     none
*/
void usart1_send_string(const char* str)
{
    if (str == NULL) return;
    
    while (*str) {
        usart1_send_char(*str++);
    }
}

/*!
    \brief      发送数据
    \param[in]  data: 要发送的数据
    \param[in]  length: 数据长度
    \param[out] none
    \retval     none
*/
void usart1_send_data(uint8_t* data, uint16_t length)
{
    if (data == NULL || length == 0) return;
    
    for (uint16_t i = 0; i < length; i++) {
        usart1_send_char((char)data[i]);
    }
}

/*!
    \brief      格式化输出函数
    \param[in]  format: 格式字符串
    \param[in]  ...: 可变参数
    \param[out] none
    \retval     发送的字符数
*/
int usart1_printf(const char* format, ...)
{
    char buffer[256];
    va_list args;
    
    va_start(args, format);
    int len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    if (len > 0) {
        usart1_send_string(buffer);
    }
    
    return len;
}

/*!
    \brief      处理命令
    \param[in]  cmd_str: 命令字符串
    \param[out] none
    \retval     none
*/
void usart1_process_command(char* cmd_str)
{
    if (cmd_str == NULL || strlen(cmd_str) == 0) {
        return;
    }

    /* 转换为小写 */
    for (int i = 0; cmd_str[i]; i++) {
        if (cmd_str[i] >= 'A' && cmd_str[i] <= 'Z') {
            cmd_str[i] = cmd_str[i] + 32;
        }
    }

    /* 处理各种命令 */
    if (strcmp(cmd_str, "help") == 0) {
        usart1_printf("Available commands:\r\n");
        usart1_printf("  help        - Show this help\r\n");
        usart1_printf("  test        - Run self test\r\n");
        usart1_printf("  loopback    - Run loopback test\r\n");
        usart1_printf("  stats       - Show statistics\r\n");
        usart1_printf("  clear       - Clear statistics\r\n");
        usart1_printf("  echo on/off - Enable/disable echo\r\n");
        usart1_printf("  baudrate    - Show current baudrate\r\n");
        usart1_printf("  status      - Show interface status\r\n");
        usart1_printf("  reset       - Reset interface\r\n");
    }
    else if (strcmp(cmd_str, "test") == 0) {
        usart1_self_test();
    }
    else if (strcmp(cmd_str, "loopback") == 0) {
        usart1_loopback_test();
    }
    else if (strcmp(cmd_str, "stats") == 0) {
        usart1_print_statistics();
    }
    else if (strcmp(cmd_str, "clear") == 0) {
        usart1_clear_statistics();
        usart1_printf("Statistics cleared\r\n");
    }
    else if (strcmp(cmd_str, "echo on") == 0) {
        usart1_echo_enabled = 1;
        usart1_printf("Echo enabled\r\n");
    }
    else if (strcmp(cmd_str, "echo off") == 0) {
        usart1_echo_enabled = 0;
        usart1_printf("Echo disabled\r\n");
    }
    else if (strcmp(cmd_str, "baudrate") == 0) {
        usart1_printf("Current baudrate: 115200\r\n");
    }
    else if (strcmp(cmd_str, "status") == 0) {
        usart1_printf("USART1 Status:\r\n");
        usart1_printf("  State: %d\r\n", usart1_state);
        usart1_printf("  Echo: %s\r\n", usart1_echo_enabled ? "ON" : "OFF");
        usart1_printf("  Last activity: %lu ms\r\n", usart1_stats.last_activity_time);
    }
    else if (strcmp(cmd_str, "reset") == 0) {
        usart1_printf("Resetting USART1 interface...\r\n");
        usart1_app_init();
    }
    else {
        usart1_printf("Unknown command: %s\r\n", cmd_str);
        usart1_printf("Type 'help' for available commands\r\n");
    }
}

/*!
    \brief      获取接收长度
    \param[in]  none
    \param[out] none
    \retval     接收到的数据长度
*/
uint16_t usart1_get_received_length(void)
{
    return usart1_received_length;
}

/*!
    \brief      清除接收缓冲区
    \param[in]  none
    \param[out] none
    \retval     none
*/
void usart1_clear_rx_buffer(void)
{
    memset(usart1_rx_buffer, 0, sizeof(usart1_rx_buffer));
    usart1_received_length = 0;
}

/*!
    \brief      获取状态
    \param[in]  none
    \param[out] none
    \retval     当前状态
*/
usart1_state_t usart1_get_state(void)
{
    return usart1_state;
}

/*!
    \brief      获取统计信息
    \param[in]  none
    \param[out] none
    \retval     统计信息指针
*/
usart1_statistics_t* usart1_get_statistics(void)
{
    return &usart1_stats;
}

/*!
    \brief      清除统计信息
    \param[in]  none
    \param[out] none
    \retval     none
*/
void usart1_clear_statistics(void)
{
    memset(&usart1_stats, 0, sizeof(usart1_statistics_t));
}

/*!
    \brief      打印统计信息
    \param[in]  none
    \param[out] none
    \retval     none
*/
void usart1_print_statistics(void)
{
    usart1_printf("=== USART1 Statistics ===\r\n");
    usart1_printf("Bytes sent: %lu\r\n", usart1_stats.bytes_sent);
    usart1_printf("Bytes received: %lu\r\n", usart1_stats.bytes_received);
    usart1_printf("Frames received: %lu\r\n", usart1_stats.frames_received);
    usart1_printf("Commands processed: %lu\r\n", usart1_stats.cmd_processed);
    usart1_printf("Errors: %lu\r\n", usart1_stats.errors);
    usart1_printf("Last activity: %lu ms\r\n", usart1_stats.last_activity_time);
    usart1_printf("========================\r\n");
}

/*!
    \brief      自检测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void usart1_self_test(void)
{
    usart1_printf("=== USART1 Self Test ===\r\n");
    usart1_printf("Hardware: USART1 (PA1/PA2/PA3)\r\n");
    usart1_printf("Baudrate: 115200\r\n");
    usart1_printf("DMA: Channel 5 (RX), Channel 7 (TX)\r\n");
    usart1_printf("Status: %s\r\n", (usart1_state == USART1_STATE_IDLE) ? "IDLE" : "BUSY");
    usart1_printf("Test completed successfully\r\n");
    usart1_printf("========================\r\n");
}

/*!
    \brief      回环测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void usart1_loopback_test(void)
{
    usart1_printf("=== USART1 Loopback Test ===\r\n");
    usart1_printf("Sending test data...\r\n");

    char test_data[] = "USART1 Test Message 12345";
    usart1_send_string(test_data);
    usart1_printf("\r\nTest data sent: %s\r\n", test_data);
    usart1_printf("============================\r\n");
}

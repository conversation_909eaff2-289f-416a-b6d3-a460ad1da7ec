# 2025年8月4日 ChatGPT对话记录

## User 1
我是一个正在学习RS485串口通信的小白，请你搜索有关资料，基于32单片机标准库的使用，生成一份使用说明文档，要让我能够完全理解如何应用即直接上手使用的程度，原理可以挑重点内容分析，我更偏向于在Keil的实际配置和应用

## GPT 1
已完成根据搜索结果编写的 RS485 使用说明文档，请查收： 

# RS485串口通信入门：基于STM32标准库的Keil应用指南

本文面向刚开始学习RS‑485串口通信的新手，结合搜索到的资料，总结了 **RS‑485 的原理、硬件连接、与 RS‑232 的区别** 以及 **在 Keil 下使用 STM32 标准外设库实现 RS‑485 通信** 的具体步骤。本文兼顾原理和实践，重点放在实际配置和编码，让你能够快速上手。

## 1&nbsp;RS‑485通信原理与特点

### 1.1 差分传输与抗干扰

RS‑485 在物理层上采用 **差分信号** 传输，两根信号线 (A、B) 的电压差代表逻辑状态。例如在发送逻辑“1”时，A 线电压高于 B 线；发送“0”时则相反。由于总线上的干扰通常同时作用于两根线，差分放大器只关注电压差，因而能抑制共模噪声，这就是 RS‑485 抗干扰强的原因[zeroinstrument.com](https://zeroinstrument.com/understanding-the-working-principle-of-rs-485-interface-a-detailed-guide/#:~:text=,Noise%20Immunity)。RS‑485 使用 **半双工** 通信，默认只用一对线即可实现双向通信；若使用两对线也可实现全双工[zeroinstrument.com](https://zeroinstrument.com/understanding-the-working-principle-of-rs-485-interface-a-detailed-guide/#:~:text=%23%203.%20Half,Transmission%20and%20Reception)。

### 1.2 多点总线与长距离能力

与 RS‑232 不同，RS‑485 支持 **多机通信**，一条总线上可以挂接多个节点（通常 32 个或更多）[zeroinstrument.com](https://zeroinstrument.com/understanding-the-working-principle-of-rs-485-interface-a-detailed-guide/#:~:text=Unlike%20the%20RS,for%20highly%20flexible%20communication%20networks)。每个节点由串口控制器和收发器组成。串口控制器与收发器之间使用 TTL 电平通信，而收发器通过差分线连接到总线[doc.embedfire.com](https://doc.embedfire.com/mcu/stm32/f407batianhu/std/zh/latest/book/RS485.html#:~:text=%E5%9B%BE%20%E5%8F%8C%E6%9C%BA%E9%80%9A%E8%AE%AF%E5%AE%9E%E9%AA%8C%E7%A1%AC%E4%BB%B6%E8%BF%9E%E6%8E%A5%E5%9B%BE_%20%E4%B8%AD%E7%9A%84%E6%98%AF%E4%B8%A4%E4%B8%AA%E5%AE%9E%E9%AA%8C%E6%9D%BF%E7%9A%84%E7%A1%AC%E4%BB%B6%E8%BF%9E%E6%8E%A5%E3%80%82%20%E5%9C%A8%E5%8D%95%E4%B8%AA%E5%AE%9E%E9%AA%8C%E6%9D%BF%E4%B8%AD%EF%BC%8C%E4%BD%9C%E4%B8%BA%E4%B8%B2%E5%8F%A3%E6%8E%A7%E5%88%B6%E5%99%A8%E7%9A%84STM32%E4%BB%8EUSART%E5%A4%96%E8%AE%BE%E5%BC%95%E5%87%BATX%E5%92%8CRX%E4%B8%A4%E4%B8%AA%E5%BC%95%E8%84%9A%E4%B8%8ERS,485%E6%80%BB%E7%BA%BF%E6%97%B6%EF%BC%8C%E6%98%AF%E4%B8%8D%E5%BA%94%E6%B7%BB%E5%8A%A0%E8%AF%A5%E7%94%B5%E9%98%BB%E7%9A%84%EF%BC%81)。由于采用差分传输，RS‑485 支持 **较长距离 (可达 1200&nbsp;m)**，并且数据速率与距离成反比[zeroinstrument.com](https://zeroinstrument.com/understanding-the-working-principle-of-rs-485-interface-a-detailed-guide/#:~:text=%23%204.%20Long,2%20Kilometers)。

### 1.3 端接电阻与布线注意事项

RS‑485 总线两端通常需要接入 **120&nbsp;Ω终端电阻**，用来匹配阻抗、防止信号反射。实验板自带该终端电阻，若作为网络中的普通节点，则不要重复加终端电阻[doc.embedfire.com](https://doc.embedfire.com/mcu/stm32/f407batianhu/std/zh/latest/book/RS485.html#:~:text=%E5%9B%BE%20%E5%8F%8C%E6%9C%BA%E9%80%9A%E8%AE%AF%E5%AE%9E%E9%AA%8C%E7%A1%AC%E4%BB%B6%E8%BF%9E%E6%8E%A5%E5%9B%BE_%20%E4%B8%AD%E7%9A%84%E6%98%AF%E4%B8%A4%E4%B8%AA%E5%AE%9E%E9%AA%8C%E6%9D%BF%E7%9A%84%E7%A1%AC%E4%BB%B6%E8%BF%9E%E6%8E%A5%E3%80%82%20%E5%9C%A8%E5%8D%95%E4%B8%AA%E5%AE%9E%E9%AA%8C%E6%9D%BF%E4%B8%AD%EF%BC%8C%E4%BD%9C%E4%B8%BA%E4%B8%B2%E5%8F%A3%E6%8E%A7%E5%88%B6%E5%99%A8%E7%9A%84STM32%E4%BB%8EUSART%E5%A4%96%E8%AE%BE%E5%BC%95%E5%87%BATX%E5%92%8CRX%E4%B8%A4%E4%B8%AA%E5%BC%95%E8%84%9A%E4%B8%8ERS,485%E6%80%BB%E7%BA%BF%E6%97%B6%EF%BC%8C%E6%98%AF%E4%B8%8D%E5%BA%94%E6%B7%BB%E5%8A%A0%E8%AF%A5%E7%94%B5%E9%98%BB%E7%9A%84%EF%BC%81)。对于每个节点，RS‑485 收发器的 A/B 脚连接到总线；在收发器与 MCU 之间使用 TTL 信号的 TX/RX 线，如 STM32 的 TX(RS485) 和 RX 引脚连接到 MAX485 模块的 DI 与 RO 引脚[doc.embedfire.com](https://doc.embedfire.com/mcu/stm32/f407batianhu/std/zh/latest/book/RS485.html#:~:text=%E5%9B%BE%20%E5%8F%8C%E6%9C%BA%E9%80%9A%E8%AE%AF%E5%AE%9E%E9%AA%8C%E7%A1%AC%E4%BB%B6%E8%BF%9E%E6%8E%A5%E5%9B%BE_%20%E4%B8%AD%E7%9A%84%E6%98%AF%E4%B8%A4%E4%B8%AA%E5%AE%9E%E9%AA%8C%E6%9D%BF%E7%9A%84%E7%A1%AC%E4%BB%B6%E8%BF%9E%E6%8E%A5%E3%80%82%20%E5%9C%A8%E5%8D%95%E4%B8%AA%E5%AE%9E%E9%AA%8C%E6%9D%BF%E4%B8%AD%EF%BC%8C%E4%BD%9C%E4%B8%BA%E4%B8%B2%E5%8F%A3%E6%8E%A7%E5%88%B6%E5%99%A8%E7%9A%84STM32%E4%BB%8EUSART%E5%A4%96%E8%AE%BE%E5%BC%95%E5%87%BATX%E5%92%8CRX%E4%B8%A4%E4%B8%AA%E5%BC%95%E8%84%9A%E4%B8%8ERS,485%E6%80%BB%E7%BA%BF%E6%97%B6%EF%BC%8C%E6%98%AF%E4%B8%8D%E5%BA%94%E6%B7%BB%E5%8A%A0%E8%AF%A5%E7%94%B5%E9%98%BB%E7%9A%84%EF%BC%81)。

### 1.4 DE/RE 引脚实现方向控制

MAX485 等收发器通常提供 **RE (Receiver Enable)** 和 **DE (Driver Enable)** 引脚，用于在半双工模式下切换收发方向。当 RE 为低电平、DE 为低电平时收发器处于接收模式；当 DE 拉高并将 RE 拉高时收发器处于发送模式[controllerstech.com](https://controllerstech.com/rs485-module-and-stm32/#:~:text=%2A%20Differential%20Signaling%20for%20Long,duplex%20communication%20between%20devices)。因此 MCU 需要通过一个 GPIO 同时控制这两个引脚，从而在发送前启动驱动器，发送完成后再恢复接收[controllerstech.com](https://controllerstech.com/rs485-module-and-stm32/#:~:text=The%20sendData%20function%20used%20above,is%20shown%20below)。

### 1.5 RS‑485 与 RS‑232、TTL 的区别

- 电平差异：RS‑232 采用单端电压，逻辑电平范围较大 (±7&nbsp;V～±12&nbsp;V)，无法直接连接到 MCU；RS‑485 使用差分信号，不同设备之间通过收发器进行电平转换[controllerstech.com](https://controllerstech.com/rs485-module-and-stm32/#:~:text=%2A%20Differential%20Signaling%20for%20Long,duplex%20communication%20between%20devices)。
- 抗干扰与距离：RS‑232 适用于短距离点对点通信；RS‑485 通过差分传输提高抗干扰能力，支持多节点和长距离通信[zeroinstrument.com](https://zeroinstrument.com/understanding-the-working-principle-of-rs-485-interface-a-detailed-guide/#:~:text=Unlike%20the%20RS,for%20highly%20flexible%20communication%20networks)。
- 网络结构：RS‑232 只能一对一，RS‑485 可以多点互联，总线拓扑更灵活。

## 2&nbsp;硬件连接与原件选择

### 2.1 MAX485 模块示例

多数开发板使用 **MAX485 (或兼容芯片)** 实现 RS‑485 与 TTL 的电平转换。该模块供电为 5&nbsp;V，TTL 端口与 MCU 相连，差分端口与总线相连。其引脚连接方式如下：

| 模块引脚 | 连接到 STM32 | 功能 |
| ---- | ---- | ---- |
| RO | MCU 的 RX 引脚 (如 PA3) | 将总线差分信号转换为 TTL 电平输出 |
| DI | MCU 的 TX 引脚 (如 PA2) | 将 MCU 的 TTL 信号送入收发器 |
| RE、DE | MCU 的一个 GPIO (如 PC0) | 控制收发器方向：RE=低、DE=低时接收；RE=高、DE=高时发送[controllerstech.com](https://controllerstech.com/rs485-module-and-stm32/#:~:text=%2A%20Differential%20Signaling%20for%20Long,duplex%20communication%20between%20devices) |
| VCC、GND | +5&nbsp;V 电源、地 | 模块供电 |
| A、B | RS‑485 总线差分线 | 与其他节点 A/B 相连，注意终端电阻配置 |

按照此连接图，将 MAX485 模块的 RO 接到 STM32 的串口接收引脚，DI 接到串口发送引脚，RE 和 DE 短接后由一个 GPIO 控制。线路应保持 A 接 A、B 接 B；如果只有两端节点，可在 A/B 之间加 120&nbsp;Ω 终端电阻[doc.embedfire.com](https://doc.embedfire.com/mcu/stm32/f407batianhu/std/zh/latest/book/RS485.html#:~:text=%E5%9B%BE%20%E5%8F%8C%E6%9C%BA%E9%80%9A%E8%AE%AF%E5%AE%9E%E9%AA%8C%E7%A1%AC%E4%BB%B6%E8%BF%9E%E6%8E%A5%E5%9B%BE_%20%E4%B8%AD%E7%9A%84%E6%98%AF%E4%B8%A4%E4%B8%AA%E5%AE%9E%E9%AA%8C%E6%9D%BF%E7%9A%84%E7%A1%AC%E4%BB%B6%E8%BF%9E%E6%8E%A5%E3%80%82%20%E5%9C%A8%E5%8D%95%E4%B8%AA%E5%AE%9E%E9%AA%8C%E6%9D%BF%E4%B8%AD%EF%BC%8C%E4%BD%9C%E4%B8%BA%E4%B8%B2%E5%8F%A3%E6%8E%A7%E5%88%B6%E5%99%A8%E7%9A%84STM32%E4%BB%8EUSART%E5%A4%96%E8%AE%BE%E5%BC%95%E5%87%BATX%E5%92%8CRX%E4%B8%A4%E4%B8%AA%E5%BC%95%E8%84%9A%E4%B8%8ERS,485%E6%80%BB%E7%BA%BF%E6%97%B6%EF%BC%8C%E6%98%AF%E4%B8%8D%E5%BA%94%E6%B7%BB%E5%8A%A0%E8%AF%A5%E7%94%B5%E9%98%BB%E7%9A%84%EF%BC%81)。

### 2.2 STM32 硬件注意事项

- 确保使用的 USART 引脚支持复用功能并在 Keil 中正确配置端口。比如在实验中使用 USART2，其 TX 引脚为 PA2，RX 为 PA3，控制引脚 PC0[doc.embedfire.com](https://doc.embedfire.com/mcu/stm32/f407batianhu/std/zh/latest/book/RS485.html#:~:text=%E5%9B%BE%20%E5%8F%8C%E6%9C%BA%E9%80%9A%E8%AE%AF%E5%AE%9E%E9%AA%8C%E7%A1%AC%E4%BB%B6%E8%BF%9E%E6%8E%A5%E5%9B%BE_%20%E4%B8%AD%E7%9A%84%E6%98%AF%E4%B8%A4%E4%B8%AA%E5%AE%9E%E9%AA%8C%E6%9D%BF%E7%9A%84%E7%A1%AC%E4%BB%B6%E8%BF%9E%E6%8E%A5%E3%80%82%20%E5%9C%A8%E5%8D%95%E4%B8%AA%E5%AE%9E%E9%AA%8C%E6%9D%BF%E4%B8%AD%EF%BC%8C%E4%BD%9C%E4%B8%BA%E4%B8%B2%E5%8F%A3%E6%8E%A7%E5%88%B6%E5%99%A8%E7%9A%84STM32%E4%BB%8EUSART%E5%A4%96%E8%AE%BE%E5%BC%95%E5%87%BATX%E5%92%8CRX%E4%B8%A4%E4%B8%AA%E5%BC%95%E8%84%9A%E4%B8%8ERS,485%E6%80%BB%E7%BA%BF%E6%97%B6%EF%BC%8C%E6%98%AF%E4%B8%8D%E5%BA%94%E6%B7%BB%E5%8A%A0%E8%AF%A5%E7%94%B5%E9%98%BB%E7%9A%84%EF%BC%81)。
- 引脚与其他功能的复用可能冲突。例如 PC0 还连接了摄像头接口，使用 RS‑485 时应断开摄像头[doc.embedfire.com](https://doc.embedfire.com/mcu/stm32/f407batianhu/std/zh/latest/book/RS485.html#:~:text=%E7%94%B1%E4%BA%8E485%E5%8F%AA%E8%83%BD%E4%BB%A5%E5%8D%8A%E5%8F%8C%E5%B7%A5%E7%9A%84%E5%BD%A2%E5%BC%8F%E5%B7%A5%E4%BD%9C%EF%BC%8C%E6%89%80%E4%BB%A5%E9%9C%80%E8%A6%81%E5%88%87%E6%8D%A2%E7%8A%B6%E6%80%81%EF%BC%8CMAX485%E8%8A%AF%E7%89%87%E4%B8%AD%E6%9C%89%E2%80%9CRE%E2%80%9D%E5%92%8C%E2%80%9CDE%E2%80%9D%E4%B8%A4%E4%B8%AA%E5%BC%95%E8%84%9A%EF%BC%8C%E7%94%A8%E4%BA%8E%E6%8E%A7%E5%88%B6485%E8%8A%AF%E7%89%87%E7%9A%84%E6%94%B6%E5%8F%91%E5%B7%A5%E4%BD%9C%E7%8A%B6%E6%80%81%E7%9A%84%EF%BC%8C%E5%BD%93RE%E5%BC%95%E8%84%9A%E4%B8%BA%E4%BD%8E%E7%94%B5%E5%B9%B3%E6%97%B6%EF%BC%8C4%2085%E8%8A%AF%E7%89%87%E5%A4%84%E4%BA%8E%E6%8E%A5%E6%94%B6%E7%8A%B6%E6%80%81%EF%BC%8C%E5%BD%93DE%E5%BC%95%E8%84%9A%E4%B8%BA%E9%AB%98%E7%94%B5%E5%B9%B3%E6%97%B6%E8%8A%AF%E7%89%87%E5%A4%84%E4%BA%8E%E5%8F%91%E9%80%81%E7%8A%B6%E6%80%81%E3%80%82%E5%AE%9E%E9%AA%8C%E6%9D%BF%E4%B8%AD%E4%BD%BF%E7%94%A8%E4%BA%86STM32%E7%9A%84PC0%E7%9B%B4%E6%8E%A5%E8%BF%9E%E6%8E%A5%E5%88%B0%E8%BF%99%E4%B8%A4%E4%B8%AA%E5%BC%95%E8%84%9A%E4%B8%8A%EF%BC%8C%E6%89%80%E4%BB%A5%E9%80%9A%E8%BF%87%E6%8E%A7%E5%88%B6PC0%E7%9A%84%E8%BE%93%E5%87%BA%20%E7%94%B5%E5%B9%B3%E5%8D%B3%E5%8F%AF%E6%8E%A7%E5%88%B6485%E7%9A%84%E6%94%B6%E5%8F%91%E7%8A%B6%E6%80%81%EF%BC%8C%E5%9C%A8%E6%9C%AC%E5%BC%80%E5%8F%91%E6%9D%BF%E4%B8%AD%EF%BC%8CPC0%E5%BC%95%E8%84%9A%E4%B8%8E%E6%91%84%E5%83%8F%E5%A4%B4%E4%BD%BF%E7%94%A8%E7%9A%84%E5%BC%95%E8%84%9A%E5%85%B1%E7%94%A8%E4%BA%86%EF%BC%8C%E6%89%80%E4%BB%A5%E4%BD%BF%E7%94%A8485%E6%97%B6%E4%B8%8D%E8%A6%81%E5%90%8C%E6%97%B6%E9%A9%B1%E5%8A%A8%E6%91%84%E5%83%8F%E5%A4%B4%E3%80%82)。
- 如果开发板默认不为 MAX485 上电，需要手动通过跳线帽连接 5&nbsp;V 供电[doc.embedfire.com](https://doc.embedfire.com/mcu/stm32/f407batianhu/std/zh/latest/book/RS485.html#:~:text=%E8%BF%98%E8%A6%81%E6%B3%A8%E6%84%8F%E7%9A%84%E6%98%AF%EF%BC%8C%E4%B8%BA%E9%98%B2%E6%AD%A2%E5%B9%B2%E6%89%B0%EF%BC%8C%E5%B9%B3%E6%97%B6%E6%88%91%E4%BB%AC%E9%BB%98%E8%AE%A4%E6%98%AF%E4%B8%8D%E7%BB%99485%E6%94%B6%E5%8F%91%E5%99%A8%E4%BE%9B%E7%94%B5%E7%9A%84%EF%BC%8C%E4%BD%BF%E7%94%A8485%E7%9A%84%E6%97%B6%E5%80%99%E4%B8%80%E5%AE%9A%E8%A6%81%E6%8A%8A485%E6%8E%A5%E7%BA%BF%E7%AB%AF%E5%AD%90%E6%97%81%E8%BE%B9%E7%9A%84%E2%80%9CC%2F4)。

## 3&nbsp;在 Keil 中基于 STM32 标准外设库实现 RS‑485

STM32 标准外设库（Standard Peripheral Library）提供一系列函数用于配置外设，避免直接操作寄存器。Keil MDK 附带了版本较旧的库，建议将适合目标芯片的库源码添加到工程中编译，以便调试和避免版本不一致的问题[community.arm.com](https://community.arm.com/support-forums/f/keil-forum/28379/stm32-using-the-st-standard-peripheral-library-with-keil#:~:text=The%20ST%20Standard%20Peripheral%20Library,deal%20directly%20with%20the%20registers)。下面给出在 Keil 中创建基于标准库的 RS‑485 工程的步骤。

### 3.1 新建工程并引入库文件

1. **创建工程目录**：例如在电脑上建立 `RS485_Project` 文件夹，其中包含 `LIB`（放置标准库源码）、`USER`（用户代码）、`MDK`（Keil 工程文件）等子目录。
2. **复制标准库源码**：从 ST 官方或安装目录中复制与所用 MCU (例如 STM32F103 或 F4 系列) 匹配的库源码 (`stm32f10x_*.c/h` 或 `stm32f4xx_*.c/h`) 到 `LIB` 目录。不要只使用 Keil 自带的库文件，而应将源文件添加到工程中，以便调试和更新[community.arm.com](https://community.arm.com/support-forums/f/keil-forum/28379/stm32-using-the-st-standard-peripheral-library-with-keil#:~:text=The%20ST%20Standard%20Peripheral%20Library,deal%20directly%20with%20the%20registers)。
3. **创建 Keil 工程**：在 `MDK` 目录中使用 Keil µVision 新建工程，选择目标芯片。将启动文件 (startup.s)、系统时钟配置文件 (system_stm32fxxx.c) 加入工程中。
4. **添加库源文件**：在工程中创建组如 “StdPeriph” 并添加所需的库源文件，例如 `stm32fxxx_rcc.c`、`stm32fxxx_gpio.c`、`stm32fxxx_usart.c` 等。确保在工程的包含路径中添加库头文件目录。
5. **配置编译选项**：根据芯片型号设置宏定义（如 `USE_STDPERIPH_DRIVER`、`STM32F10X_HD`），以便编译器找到合适的头文件。

### 3.2 初始化硬件

下面以 STM32F4 为例说明初始化 RS‑485 所用的 USART2 和控制引脚。代码使用标准库函数，用户可根据实际芯片调整时钟和引脚宏。

```
/* bsp_485.h – RS-485 硬件配置宏 */
#define RS485_USART             USART2
#define RS485_USART_CLK         RCC_APB1Periph_USART2
#define RS485_USART_BAUDRATE    115200

/* RX 引脚 */
#define RS485_RX_GPIO_PORT      GPIOA
#define RS485_RX_GPIO_CLK       RCC_AHB1Periph_GPIOA
#define RS485_RX_PIN            GPIO_Pin_3
#define RS485_RX_AF             GPIO_AF_USART2
#define RS485_RX_SOURCE         GPIO_PinSource3

/* TX 引脚 */
#define RS485_TX_GPIO_PORT      GPIOA
#define RS485_TX_GPIO_CLK       RCC_AHB1Periph_GPIOA
#define RS485_TX_PIN            GPIO_Pin_2
#define RS485_TX_AF             GPIO_AF_USART2
#define RS485_TX_SOURCE         GPIO_PinSource2

/* 485 收发控制引脚 */
#define RS485_DIR_GPIO_PORT     GPIOC
#define RS485_DIR_GPIO_CLK      RCC_AHB1Periph_GPIOC
#define RS485_DIR_PIN           GPIO_Pin_0

/* 中断定义 */
#define RS485_IRQ               USART2_IRQn
#define RS485_IRQHandler        USART2_IRQHandler

```

**初始化函数：**

```
void RS485_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;

    /* 开启时钟 */
    RCC_AHB1PeriphClockCmd(RS485_RX_GPIO_CLK | RS485_TX_GPIO_CLK | RS485_DIR_GPIO_CLK, ENABLE);
    RCC_APB1PeriphClockCmd(RS485_USART_CLK, ENABLE);

    /* 配置 TX 和 RX 引脚的复用功能 */
    GPIO_PinAFConfig(RS485_TX_GPIO_PORT, RS485_TX_SOURCE, RS485_TX_AF);
    GPIO_PinAFConfig(RS485_RX_GPIO_PORT, RS485_RX_SOURCE, RS485_RX_AF);

    /* 初始化 TX 引脚 */
    GPIO_InitStructure.GPIO_Pin   = RS485_TX_PIN;
    GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd  = GPIO_PuPd_UP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(RS485_TX_GPIO_PORT, &GPIO_InitStructure);

    /* 初始化 RX 引脚 */
    GPIO_InitStructure.GPIO_Pin   = RS485_RX_PIN;
    GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_AF;
    GPIO_Init(RS485_RX_GPIO_PORT, &GPIO_InitStructure);

    /* 初始化方向控制引脚 (RE/DE) */
    GPIO_InitStructure.GPIO_Pin   = RS485_DIR_PIN;
    GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd  = GPIO_PuPd_UP;
    GPIO_Init(RS485_DIR_GPIO_PORT, &GPIO_InitStructure);

    /* 默认置为接收模式 */
    GPIO_ResetBits(RS485_DIR_GPIO_PORT, RS485_DIR_PIN);

    /* 配置 USART 参数 */
    USART_InitStructure.USART_BaudRate   = RS485_USART_BAUDRATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits   = USART_StopBits_1;
    USART_InitStructure.USART_Parity     = USART_Parity_No;
    USART_InitStructure.USART_Mode       = USART_Mode_Rx | USART_Mode_Tx;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_Init(RS485_USART, &USART_InitStructure);

    /* 使能 USART 收发及中断 */
    USART_ITConfig(RS485_USART, USART_IT_RXNE, ENABLE);
    NVIC_EnableIRQ(RS485_IRQ);
    USART_Cmd(RS485_USART, ENABLE);
}

```

上述代码完成时钟启用、GPIO 初始化以及串口参数配置，并开启接收中断。默认将方向控制引脚拉低，使 MAX485 处于接收状态[controllerstech.com](https://controllerstech.com/rs485-module-and-stm32/#:~:text=The%20sendData%20function%20used%20above,is%20shown%20below)。若芯片支持 **硬件 RS‑485 模式**，可以使用标准库中的 **驱动使能控制函数**：

```
/* 当 MCU 支持 RS‑485 硬件驱动，例如 STM32F303、F0、F7 的部分 USART */
USART_Init(RS485_USART, &USART_InitStructure);
USART_SetDEAssertionTime(RS485_USART, 0x1);     // 设置驱动使能断言时间
USART_SetDEDeassertionTime(RS485_USART, 0x1);    // 设置驱动使能取消时间
USART_DEPolarityConfig(RS485_USART, USART_DEPolarity_High); // DE 引脚为高有效
USART_DECmd(RS485_USART, ENABLE);                // 使能驱动使能 (DE) 功能:contentReference[oaicite:19]{index=19}
USART_Cmd(RS485_USART, ENABLE);

```

这些函数来源于标准库的 RS‑485 支持模块，它们允许硬件自动控制 DE 引脚，从而无需手动切换 RE/DE[docs.ros.org](https://docs.ros.org/en/kinetic/api/rosflight_firmware/html/group__USART__Group10.html#:~:text=,depending%20on%20the%20oversampling%20rate)。对于不支持该硬件特性的芯片，可以使用前面手动控制 GPIO 的方式。

### 3.3 方向控制和数据发送

半双工通信需要在发送前切换为发送状态，发送结束后立即切回接收状态。以下是常见的宏和发送函数示例：

```
/* 在 bsp_485.h 中定义方向控制宏 */
#define RS485_RX_MODE()  GPIO_ResetBits(RS485_DIR_GPIO_PORT, RS485_DIR_PIN) // 低电平接收
#define RS485_TX_MODE()  GPIO_SetBits(RS485_DIR_GPIO_PORT, RS485_DIR_PIN)   // 高电平发送

/* 发送单个字节 */
void RS485_SendByte(uint8_t byte)
{
    RS485_TX_MODE();                    // 切换为发送状态
    USART_SendData(RS485_USART, byte);  // 将数据写入串口数据寄存器
    while (USART_GetFlagStatus(RS485_USART, USART_FLAG_TXE) == RESET);
    while (USART_GetFlagStatus(RS485_USART, USART_FLAG_TC) == RESET);
    RS485_RX_MODE();                    // 发送完成后恢复接收状态:contentReference[oaicite:21]{index=21}
}

/* 发送数组 */
void RS485_SendArray(uint8_t *buf, uint16_t len)
{
    uint16_t i;
    RS485_TX_MODE();
    for(i=0; i

在中断服务函数里接收数据时，可通过 `USART_GetITStatus()` 判断 `USART_IT_RXNE` 标志，从数据寄存器读取字节并存入缓冲区。由于实验板使用 PC0 同时连接 MAX485 的 RE 和 DE 引脚[doc.embedfire.com](https://doc.embedfire.com/mcu/stm32/f407batianhu/std/zh/latest/book/RS485.html#:~:text=%E7%94%B1%E4%BA%8E485%E5%8F%AA%E8%83%BD%E4%BB%A5%E5%8D%8A%E5%8F%8C%E5%B7%A5%E7%9A%84%E5%BD%A2%E5%BC%8F%E5%B7%A5%E4%BD%9C%EF%BC%8C%E6%89%80%E4%BB%A5%E9%9C%80%E8%A6%81%E5%88%87%E6%8D%A2%E7%8A%B6%E6%80%81%EF%BC%8CMAX485%E8%8A%AF%E7%89%87%E4%B8%AD%E6%9C%89%E2%80%9CRE%E2%80%9D%E5%92%8C%E2%80%9CDE%E2%80%9D%E4%B8%A4%E4%B8%AA%E5%BC%95%E8%84%9A%EF%BC%8C%E7%94%A8%E4%BA%8E%E6%8E%A7%E5%88%B6485%E8%8A%AF%E7%89%87%E7%9A%84%E6%94%B6%E5%8F%91%E5%B7%A5%E4%BD%9C%E7%8A%B6%E6%80%81%E7%9A%84%EF%BC%8C%E5%BD%93RE%E5%BC%95%E8%84%9A%E4%B8%BA%E4%BD%8E%E7%94%B5%E5%B9%B3%E6%97%B6%EF%BC%8C4%2085%E8%8A%AF%E7%89%87%E5%A4%84%E4%BA%8E%E6%8E%A5%E6%94%B6%E7%8A%B6%E6%80%81%EF%BC%8C%E5%BD%93DE%E5%BC%95%E8%84%9A%E4%B8%BA%E9%AB%98%E7%94%B5%E5%B9%B3%E6%97%B6%E8%8A%AF%E7%89%87%E5%A4%84%E4%BA%8E%E5%8F%91%E9%80%81%E7%8A%B6%E6%80%81%E3%80%82%E5%AE%9E%E9%AA%8C%E6%9D%BF%E4%B8%AD%E4%BD%BF%E7%94%A8%E4%BA%86STM32%E7%9A%84PC0%E7%9B%B4%E6%8E%A5%E8%BF%9E%E6%8E%A5%E5%88%B0%E8%BF%99%E4%B8%A4%E4%B8%AA%E5%BC%95%E8%84%9A%E4%B8%8A%EF%BC%8C%E6%89%80%E4%BB%A5%E9%80%9A%E8%BF%87%E6%8E%A7%E5%88%B6PC0%E7%9A%84%E8%BE%93%E5%87%BA%20%E7%94%B5%E5%B9%B3%E5%8D%B3%E5%8F%AF%E6%8E%A7%E5%88%B6485%E7%9A%84%E6%94%B6%E5%8F%91%E7%8A%B6%E6%80%81%EF%BC%8C%E5%9C%A8%E6%9C%AC%E5%BC%80%E5%8F%91%E6%9D%BF%E4%B8%AD%EF%BC%8CPC0%E5%BC%95%E8%84%9A%E4%B8%8E%E6%91%84%E5%83%8F%E5%A4%B4%E4%BD%BF%E7%94%A8%E7%9A%84%E5%BC%95%E8%84%9A%E5%85%B1%E7%94%A8%E4%BA%86%EF%BC%8C%E6%89%80%E4%BB%A5%E4%BD%BF%E7%94%A8485%E6%97%B6%E4%B8%8D%E8%A6%81%E5%90%8C%E6%97%B6%E9%A9%B1%E5%8A%A8%E6%91%84%E5%83%8F%E5%A4%B4%E3%80%82)，切换收发状态只需控制一个 GPIO。

### 3.4 main 函数示例

下面给出一个主函数示例：当检测到按键被按下时，发送 256 个字节；接收到数据后通过串口打印调试。可根据实际需要修改。

```
int main(void)
{
    uint16_t i;
    uint8_t txData[256];

    /* 初始化系统时钟和外设 */
    SysTick_Config(SystemCoreClock / 1000);
    RS485_Init();
    Key_Init();    // 初始化按键(用户自定义)
    Debug_USART_Init(); // 初始化调试串口

    for(i=0; i&lt;256; i++) txData[i] = i; // 准备测试数据

    while (1)
    {
        if(Key_Pressed())
        {
            RS485_SendArray(txData, 256);
        }
        if(rs485_rx_flag)  // 自定义标志，指示已接收到 256 个数据
        {
            rs485_rx_flag = 0;
            printf("Received data:\n");
            for(i=0; i&lt;256; i++) {
                printf("%02X ", rs485_rx_buf[i]);
            }
            printf("\n");
        }
    }
}

```

在实现中需要编写 USART2 的中断处理函数，通过缓冲区接收字节并设置 `rs485_rx_flag`。在使用 Keil 调试时，可利用串口打印观察发送与接收情况。

## 4&nbsp;调试与实用建议

- **共用引脚冲突**：某些开发板的 RS‑485 控制引脚与其他外设共用，使用时注意不要同时启用相关外设[doc.embedfire.com](https://doc.embedfire.com/mcu/stm32/f407batianhu/std/zh/latest/book/RS485.html#:~:text=%E7%94%B1%E4%BA%8E485%E5%8F%AA%E8%83%BD%E4%BB%A5%E5%8D%8A%E5%8F%8C%E5%B7%A5%E7%9A%84%E5%BD%A2%E5%BC%8F%E5%B7%A5%E4%BD%9C%EF%BC%8C%E6%89%80%E4%BB%A5%E9%9C%80%E8%A6%81%E5%88%87%E6%8D%A2%E7%8A%B6%E6%80%81%EF%BC%8CMAX485%E8%8A%AF%E7%89%87%E4%B8%AD%E6%9C%89%E2%80%9CRE%E2%80%9D%E5%92%8C%E2%80%9CDE%E2%80%9D%E4%B8%A4%E4%B8%AA%E5%BC%95%E8%84%9A%EF%BC%8C%E7%94%A8%E4%BA%8E%E6%8E%A7%E5%88%B6485%E8%8A%AF%E7%89%87%E7%9A%84%E6%94%B6%E5%8F%91%E5%B7%A5%E4%BD%9C%E7%8A%B6%E6%80%81%E7%9A%84%EF%BC%8C%E5%BD%93RE%E5%BC%95%E8%84%9A%E4%B8%BA%E4%BD%8E%E7%94%B5%E5%B9%B3%E6%97%B6%EF%BC%8C4%2085%E8%8A%AF%E7%89%87%E5%A4%84%E4%BA%8E%E6%8E%A5%E6%94%B6%E7%8A%B6%E6%80%81%EF%BC%8C%E5%BD%93DE%E5%BC%95%E8%84%9A%E4%B8%BA%E9%AB%98%E7%94%B5%E5%B9%B3%E6%97%B6%E8%8A%AF%E7%89%87%E5%A4%84%E4%BA%8E%E5%8F%91%E9%80%81%E7%8A%B6%E6%80%81%E3%80%82%E5%AE%9E%E9%AA%8C%E6%9D%BF%E4%B8%AD%E4%BD%BF%E7%94%A8%E4%BA%86STM32%E7%9A%84PC0%E7%9B%B4%E6%8E%A5%E8%BF%9E%E6%8E%A5%E5%88%B0%E8%BF%99%E4%B8%A4%E4%B8%AA%E5%BC%95%E8%84%9A%E4%B8%8A%EF%BC%8C%E6%89%80%E4%BB%A5%E9%80%9A%E8%BF%87%E6%8E%A7%E5%88%B6PC0%E7%9A%84%E8%BE%93%E5%87%BA%20%E7%94%B5%E5%B9%B3%E5%8D%B3%E5%8F%AF%E6%8E%A7%E5%88%B6485%E7%9A%84%E6%94%B6%E5%8F%91%E7%8A%B6%E6%80%81%EF%BC%8C%E5%9C%A8%E6%9C%AC%E5%BC%80%E5%8F%91%E6%9D%BF%E4%B8%AD%EF%BC%8CPC0%E5%BC%95%E8%84%9A%E4%B8%8E%E6%91%84%E5%83%8F%E5%A4%B4%E4%BD%BF%E7%94%A8%E7%9A%84%E5%BC%95%E8%84%9A%E5%85%B1%E7%94%A8%E4%BA%86%EF%BC%8C%E6%89%80%E4%BB%A5%E4%BD%BF%E7%94%A8485%E6%97%B6%E4%B8%8D%E8%A6%81%E5%90%8C%E6%97%B6%E9%A9%B1%E5%8A%A8%E6%91%84%E5%83%8F%E5%A4%B4%E3%80%82)。
- **供电与终端电阻**：确认收发器已供电并在网络两端添加 120&nbsp;Ω 终端电阻，若仅两块板相连，可每端添加一个终端电阻[doc.embedfire.com](https://doc.embedfire.com/mcu/stm32/f407batianhu/std/zh/latest/book/RS485.html#:~:text=%E5%9B%BE%20%E5%8F%8C%E6%9C%BA%E9%80%9A%E8%AE%AF%E5%AE%9E%E9%AA%8C%E7%A1%AC%E4%BB%B6%E8%BF%9E%E6%8E%A5%E5%9B%BE_%20%E4%B8%AD%E7%9A%84%E6%98%AF%E4%B8%A4%E4%B8%AA%E5%AE%9E%E9%AA%8C%E6%9D%BF%E7%9A%84%E7%A1%AC%E4%BB%B6%E8%BF%9E%E6%8E%A5%E3%80%82%20%E5%9C%A8%E5%8D%95%E4%B8%AA%E5%AE%9E%E9%AA%8C%E6%9D%BF%E4%B8%AD%EF%BC%8C%E4%BD%9C%E4%B8%BA%E4%B8%B2%E5%8F%A3%E6%8E%A7%E5%88%B6%E5%99%A8%E7%9A%84STM32%E4%BB%8EUSART%E5%A4%96%E8%AE%BE%E5%BC%95%E5%87%BATX%E5%92%8CRX%E4%B8%A4%E4%B8%AA%E5%BC%95%E8%84%9A%E4%B8%8ERS,485%E6%80%BB%E7%BA%BF%E6%97%B6%EF%BC%8C%E6%98%AF%E4%B8%8D%E5%BA%94%E6%B7%BB%E5%8A%A0%E8%AF%A5%E7%94%B5%E9%98%BB%E7%9A%84%EF%BC%81)。使用多节点时只在最远的两个节点加终端电阻。
- **半双工发送冲突**：在多节点环境下，保证同一时刻只有一个节点发送数据；可采用主从协议或增加总线仲裁机制。
- **硬件 RS‑485 模式**：部分 STM32 (如 F303、F746) 的 USART 提供硬件驱动使能 (DE) 功能。使用标准库的 `USART_DECmd()` 和 `USART_SetDEAssertionTime()` 等函数即可自动控制 DE 引脚[docs.ros.org](https://docs.ros.org/en/kinetic/api/rosflight_firmware/html/group__USART__Group10.html#:~:text=RS485%20mode%20function)。若使用的是不支持该功能的 MCU，则需通过 GPIO 手动控制 DE/RE[controllerstech.com](https://controllerstech.com/rs485-module-and-stm32/#:~:text=The%20sendData%20function%20used%20above,is%20shown%20below)。
- **库版本一致性**：确保使用的标准库版本与芯片匹配；在 Keil 项目中删除旧版库，避免不同版本代码混用[community.arm.com](https://community.arm.com/support-forums/f/keil-forum/28379/stm32-using-the-st-standard-peripheral-library-with-keil#:~:text=The%20ST%20Standard%20Peripheral%20Library,deal%20directly%20with%20the%20registers)。

## 5&nbsp;总结

RS‑485 通过差分传输实现抗干扰和长距离多点通信，是工业控制领域常见的串口物理层标准。使用 STM32 实现 RS‑485 通信需要接入 MAX485 等收发器，注意 RE/DE 引脚控制。基于标准外设库，在 Keil 中配置 USART、GPIO 和中断等外设并不复杂：初始化串口、切换收发方向、收发数据。部分芯片支持硬件驱动使能，使用库函数能简化控制过程。希望本文的说明能帮助你快速搭建 RS‑485 串口通信系统，并在实践中深入理解这一技术。
。欢迎提出进一步的需求。

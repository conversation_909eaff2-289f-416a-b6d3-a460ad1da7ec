Dependencies for Project '2025916750', Target 'Target_1': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (..\intro.txt)(0x685168CE)()
F (..\sysfunction\btn_app.c)(0x685149B8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\btn_app.o --omf_browse .\objects\btn_app.crf --depend .\objects\btn_app.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\sysfunction\led_app.c)(0x685149E7)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\led_app.o --omf_browse .\objects\led_app.crf --depend .\objects\led_app.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\sysfunction\oled_app.c)(0x68514DBD)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\oled_app.o --omf_browse .\objects\oled_app.crf --depend .\objects\oled_app.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\sysfunction\rtc_app.c)(0x68514E47)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\rtc_app.o --omf_browse .\objects\rtc_app.crf --depend .\objects\rtc_app.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\sysfunction\scheduler.c)(0x68908B1D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\scheduler.o --omf_browse .\objects\scheduler.crf --depend .\objects\scheduler.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\sysfunction\sd_app.c)(0x68516706)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\sd_app.o --omf_browse .\objects\sd_app.crf --depend .\objects\sd_app.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\sysfunction\usart_app.c)(0x68908498)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\usart_app.o --omf_browse .\objects\usart_app.crf --depend .\objects\usart_app.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\sysfunction\log_app.c)(0x6890299A)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\log_app.o --omf_browse .\objects\log_app.crf --depend .\objects\log_app.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\sysfunction\rs485_app.c)(0x68908798)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\rs485_app.o --omf_browse .\objects\rs485_app.crf --depend .\objects\rs485_app.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\Hardware\bsp\cmic_gd32f470vet6.c)(0x68908B06)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\cmic_gd32f470vet6.o --omf_browse .\objects\cmic_gd32f470vet6.crf --depend .\objects\cmic_gd32f470vet6.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\Hardware\fatfs\diskio.c)(0x684ED09B)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\diskio.o --omf_browse .\objects\diskio.crf --depend .\objects\diskio.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\Hardware\fatfs\ff.c)(0x684F666B)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\ff.o --omf_browse .\objects\ff.crf --depend .\objects\ff.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
F (..\Hardware\gd25qxx\gd25qxx.c)(0x684BFF05)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd25qxx.o --omf_browse .\objects\gd25qxx.crf --depend .\objects\gd25qxx.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\Hardware\gd25qxx\lfs.c)(0x684C086E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\lfs.o --omf_browse .\objects\lfs.crf --depend .\objects\lfs.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\gd25qxx\lfs.h)(0x684B7E4F)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (..\Hardware\gd25qxx\lfs_util.h)(0x684B7E4F)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\inttypes.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\assert.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
F (..\Hardware\gd25qxx\lfs_port.c)(0x68514554)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\lfs_port.o --omf_browse .\objects\lfs_port.crf --depend .\objects\lfs_port.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\gd25qxx\lfs_port.h)(0x684BBF27)
I (..\Hardware\gd25qxx\lfs.h)(0x684B7E4F)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
F (..\Hardware\gd25qxx\lfs_util.c)(0x684B7E4F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\lfs_util.o --omf_browse .\objects\lfs_util.crf --depend .\objects\lfs_util.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\gd25qxx\lfs_util.h)(0x684B7E4F)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\inttypes.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\assert.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
F (..\Hardware\oled\oled.c)(0x684B7E4F)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\oled.o --omf_browse .\objects\oled.crf --depend .\objects\oled.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (..\Hardware\oled\oledfont.h)(0x684B7E4F)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
F (..\Hardware\sdio\sdio_sdcard.c)(0x684ECE0A)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\sdio_sdcard.o --omf_browse .\objects\sdio_sdcard.crf --depend .\objects\sdio_sdcard.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
F (..\Hardware\fatfs\unicode.c)(0x684E8703)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\unicode.o --omf_browse .\objects\unicode.crf --depend .\objects\unicode.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
F (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)()
F (..\Library\Source\gd32f4xx_adc.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_can.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_can.o --omf_browse .\objects\gd32f4xx_can.crf --depend .\objects\gd32f4xx_can.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_crc.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_crc.o --omf_browse .\objects\gd32f4xx_crc.crf --depend .\objects\gd32f4xx_crc.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_ctc.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_ctc.o --omf_browse .\objects\gd32f4xx_ctc.crf --depend .\objects\gd32f4xx_ctc.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_dac.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_dac.o --omf_browse .\objects\gd32f4xx_dac.crf --depend .\objects\gd32f4xx_dac.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_dbg.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_dbg.o --omf_browse .\objects\gd32f4xx_dbg.crf --depend .\objects\gd32f4xx_dbg.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_dci.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_dci.o --omf_browse .\objects\gd32f4xx_dci.crf --depend .\objects\gd32f4xx_dci.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_dma.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_dma.o --omf_browse .\objects\gd32f4xx_dma.crf --depend .\objects\gd32f4xx_dma.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_enet.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_enet.o --omf_browse .\objects\gd32f4xx_enet.crf --depend .\objects\gd32f4xx_enet.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
F (..\Library\Source\gd32f4xx_exmc.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_exmc.o --omf_browse .\objects\gd32f4xx_exmc.crf --depend .\objects\gd32f4xx_exmc.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_exti.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_exti.o --omf_browse .\objects\gd32f4xx_exti.crf --depend .\objects\gd32f4xx_exti.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_fmc.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_fmc.o --omf_browse .\objects\gd32f4xx_fmc.crf --depend .\objects\gd32f4xx_fmc.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_fwdgt.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_fwdgt.o --omf_browse .\objects\gd32f4xx_fwdgt.crf --depend .\objects\gd32f4xx_fwdgt.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_gpio.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_gpio.o --omf_browse .\objects\gd32f4xx_gpio.crf --depend .\objects\gd32f4xx_gpio.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_i2c.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_i2c.o --omf_browse .\objects\gd32f4xx_i2c.crf --depend .\objects\gd32f4xx_i2c.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_ipa.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_ipa.o --omf_browse .\objects\gd32f4xx_ipa.crf --depend .\objects\gd32f4xx_ipa.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_iref.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_iref.o --omf_browse .\objects\gd32f4xx_iref.crf --depend .\objects\gd32f4xx_iref.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_misc.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_misc.o --omf_browse .\objects\gd32f4xx_misc.crf --depend .\objects\gd32f4xx_misc.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_pmu.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_pmu.o --omf_browse .\objects\gd32f4xx_pmu.crf --depend .\objects\gd32f4xx_pmu.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_rcu.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_rcu.o --omf_browse .\objects\gd32f4xx_rcu.crf --depend .\objects\gd32f4xx_rcu.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_rtc.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_rtc.o --omf_browse .\objects\gd32f4xx_rtc.crf --depend .\objects\gd32f4xx_rtc.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_sdio.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_sdio.o --omf_browse .\objects\gd32f4xx_sdio.crf --depend .\objects\gd32f4xx_sdio.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_spi.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_spi.o --omf_browse .\objects\gd32f4xx_spi.crf --depend .\objects\gd32f4xx_spi.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_syscfg.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_syscfg.o --omf_browse .\objects\gd32f4xx_syscfg.crf --depend .\objects\gd32f4xx_syscfg.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_timer.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_timer.o --omf_browse .\objects\gd32f4xx_timer.crf --depend .\objects\gd32f4xx_timer.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_tli.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_tli.o --omf_browse .\objects\gd32f4xx_tli.crf --depend .\objects\gd32f4xx_tli.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_trng.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_trng.o --omf_browse .\objects\gd32f4xx_trng.crf --depend .\objects\gd32f4xx_trng.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_usart.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_usart.o --omf_browse .\objects\gd32f4xx_usart.crf --depend .\objects\gd32f4xx_usart.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Library\Source\gd32f4xx_wwdgt.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_wwdgt.o --omf_browse .\objects\gd32f4xx_wwdgt.crf --depend .\objects\gd32f4xx_wwdgt.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\Startup\startup_gd32f450_470.s)(0x684EC35C)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

--pd "__UVISION_VERSION SETA 541" --pd "GD32F470 SETA 1" --pd "_RTE_ SETA 1"

--list .\listings\startup_gd32f450_470.lst --xref -o .\objects\startup_gd32f450_470.o --depend .\objects\startup_gd32f450_470.d)
F (..\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x684B7E50)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\system_gd32f4xx.o --omf_browse .\objects\system_gd32f4xx.crf --depend .\objects\system_gd32f4xx.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
F (..\User\source\gd32f4xx_it.c)(0x684B7E51)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\gd32f4xx_it.o --omf_browse .\objects\gd32f4xx_it.crf --depend .\objects\gd32f4xx_it.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\User\include\gd32f4xx_it.h)(0x684B7E51)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\main.h)(0x684B7E51)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\User\source\main.c)(0x68908B30)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\Hardware\bsp\cmic_gd32f470vet6.h)(0x68908AF2)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
I (..\Hardware\oled\oled.h)(0x684B7E4F)
I (..\Hardware\gd25qxx\gd25qxx.h)(0x684B7E4F)
I (..\Hardware\sdio\sdio_sdcard.h)(0x684B7E4F)
I (..\Hardware\fatfs\ff.h)(0x684EB39F)
I (..\Hardware\fatfs\ffconf.h)(0x684F6530)
I (..\Hardware\fatfs\diskio.h)(0x684EA73C)
I (..\sysfunction\sd_app.h)(0x68514E92)
I (..\sysfunction\led_app.h)(0x684B7E4F)
I (..\sysfunction\oled_app.h)(0x684B7E4F)
I (..\sysfunction\usart_app.h)(0x68514A51)
I (..\sysfunction\usart1_app.h)(0x68908B43)
I (D:\Keil\ARM\ARMCC\include\stdio.h)(0x5E8E3CC2)
I (..\sysfunction\rs485_app.h)(0x68907A36)
I (..\sysfunction\rtc_app.h)(0x68514DE9)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (..\sysfunction\btn_app.h)(0x684FAEB5)
I (..\sysfunction\scheduler.h)(0x684B7E4F)
I (..\sysfunction\log_app.h)(0x68901C72)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stdarg.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
F (..\User\source\systick.c)(0x684B7E51)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\systick.o --omf_browse .\objects\systick.crf --depend .\objects\systick.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x684B7E50)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\core_cm4.h)(0x6842F7BF)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_version.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\mpu_armv7.h)(0x6842F7BF)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x684B7E50)
I (..\User\include\gd32f4xx_libopt.h)(0x684B7E51)
I (..\Library\Include\gd32f4xx_rcu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_adc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_can.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_crc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ctc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dac.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dbg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dci.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_dma.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_exti.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_fwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_gpio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_syscfg.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_i2c.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_iref.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_pmu.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_rtc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_sdio.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_spi.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_timer.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_trng.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_usart.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_wwdgt.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_misc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_enet.h)(0x684B7E50)
I (D:\Keil\ARM\ARMCC\include\stdlib.h)(0x5E8E3CC2)
I (..\Library\Include\gd32f4xx_exmc.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_ipa.h)(0x684B7E50)
I (..\Library\Include\gd32f4xx_tli.h)(0x684B7E50)
I (..\User\include\systick.h)(0x684B7E51)
F (D:/Keil/ARM/Packs/GorgonMeducer/perf_counter/2.3.3/perf_counter.c)(0x684640B3)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\perf_counter.o --omf_browse .\objects\perf_counter.crf --depend .\objects\perf_counter.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:/Keil/ARM/Packs/GorgonMeducer/perf_counter/2.3.3/perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (D:/Keil/ARM/Packs/GorgonMeducer/perf_counter/2.3.3/perfc_port_default.h)(0x684640B3)
F (D:/Keil/ARM/Packs/GorgonMeducer/perf_counter/2.3.3/perfc_port_default.c)(0x684640B3)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O1 --apcs=interwork --split_sections -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Hardware\bsp -I ..\Hardware\fatfs -I ..\Hardware\gd25qxx -I ..\Hardware\oled -I ..\Hardware\sdio -I ..\Library\Include -I ..\sysfunction -I ..\system -I ..\User\include -I ..\PACK\perf_counter-CMSIS-Pack\cmsis-pack

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  --preinclude="Pre_Include_Global.h"

-o .\objects\perfc_port_default.o --omf_browse .\objects\perfc_port_default.crf --depend .\objects\perfc_port_default.d)
I (.\RTE\_Target_1\Pre_Include_Global.h)(0x684BF2D7)
I (D:\Keil\ARM\ARMCC\include\stdint.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\stdbool.h)(0x5E8E3CC2)
I (D:\Keil\ARM\ARMCC\include\string.h)(0x5E8E3CC2)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_compiler.h)(0x6842F7BF)
I (D:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include\cmsis_armcc.h)(0x6842F7BF)
I (D:/Keil/ARM/Packs/GorgonMeducer/perf_counter/2.3.3/perf_counter.h)(0x684640B3)
I (D:\Keil\ARM\ARMCC\include\stddef.h)(0x5E8E3CC2)
I (D:/Keil/ARM/Packs/GorgonMeducer/perf_counter/2.3.3/perfc_port_default.h)(0x684640B3)
F (D:/Keil/ARM/Packs/GorgonMeducer/perf_counter/2.3.3/systick_wrapper_ual.s)(0x684640B3)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-I.\RTE\_Target_1

-ID:\Keil\ARM\Packs\ARM\CMSIS\5.7.0\CMSIS\Core\Include

-ID:\Keil\ARM\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-ID:\Keil\ARM\Packs\GorgonMeducer\perf_counter\2.3.3

--pd "__UVISION_VERSION SETA 541" --pd "GD32F470 SETA 1" --pd "_RTE_ SETA 1"

--list .\listings\systick_wrapper_ual.lst --xref -o .\objects\systick_wrapper_ual.o --depend .\objects\systick_wrapper_ual.d)

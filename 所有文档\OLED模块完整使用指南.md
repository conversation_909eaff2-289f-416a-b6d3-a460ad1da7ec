# OLED模块使用指南

## 概述

本系统提供OLED显示功能，支持数字、字母、基本符号的显示。OLED屏幕用于显示系统状态和实时数据。

## 核心文件和函数位置

### 主要文件
- **bsp_oled.c** - OLED底层驱动实现
- **bsp_oled.h** - OLED底层驱动头文件
- **main.c** - OLED初始化调用

## 1. OLED初始化

### 1.1 OLED初始化
```c
// 位置：bsp_oled.c
void OLED_Init(void)
```
**作用**：初始化OLED硬件和显示控制器

**调用位置**：main.c中的系统初始化
```c
void main(void) {
    // 其他硬件初始化...
    OLED_Init();  // OLED初始化

    // 显示欢迎信息
    OLED_ShowString(0, 0, "System Ready", 16);
    OLED_Refresh_Gram();
}
```

**说明**：初始化后OLED即可正常显示，分辨率为128x64像素

## 2. 基础显示功能

### 2.1 清屏和刷新
```c
// 位置：bsp_oled.c
void OLED_Clear(void)              // 清空显存
void OLED_Refresh_Gram(void)       // 刷新显存到屏幕
```

**使用方法**：
```c
OLED_Clear();           // 清空显存
OLED_Refresh_Gram();    // 刷新到屏幕显示
```

**重要说明**：所有显示操作后都需要调用`OLED_Refresh_Gram()`才能在屏幕上看到效果

## 3. 文字和数字显示

### 3.1 字符显示
```c
// 位置：bsp_oled.c
void OLED_ShowChar(uint8_t x, uint8_t y, uint8_t chr, uint8_t size)
```
**作用**：显示单个字符（字母、数字、符号）

**参数说明**：
- `x`: X坐标 (0-127)
- `y`: Y坐标 (0-63)
- `chr`: 字符 ('A'-'Z', 'a'-'z', '0'-'9', 符号)
- `size`: 字体大小 (12/16)

### 3.2 字符串显示
```c
// 位置：bsp_oled.c
void OLED_ShowString(uint8_t x, uint8_t y, char *p, uint8_t size)
```
**作用**：显示字符串

**调用示例**：
```c
// 显示系统信息
OLED_Clear();
OLED_ShowString(0, 0, "CMIC System", 16);
OLED_ShowString(0, 20, "Ready", 12);
OLED_Refresh_Gram();
```

### 3.3 数字显示
```c
// 位置：bsp_oled.c
void OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, uint8_t size)
```
**作用**：显示数字

**调用示例**：
```c
// 显示电压值：3.25V
uint32_t voltage = 325;  // 3.25V * 100
OLED_ShowString(0, 0, "Voltage:", 12);
OLED_ShowNum(60, 0, voltage/100, 1, 12);    // 显示整数部分 "3"
OLED_ShowString(72, 0, ".", 12);            // 显示小数点
OLED_ShowNum(80, 0, voltage%100, 2, 12);    // 显示小数部分 "25"
OLED_ShowString(104, 0, "V", 12);           // 显示单位
OLED_Refresh_Gram();
```

## 4. 实际使用示例

### 4.1 显示系统状态
```c
void display_system_status(void) {
    OLED_Clear();

    // 显示标题
    OLED_ShowString(0, 0, "System Status", 16);

    // 显示状态信息
    OLED_ShowString(0, 20, "Flash: OK", 12);
    OLED_ShowString(0, 32, "TF Card: OK", 12);
    OLED_ShowString(0, 44, "RTC: Running", 12);

    OLED_Refresh_Gram();
}
```

### 4.2 显示实时数据
```c
void display_voltage_data(uint16_t voltage_mv) {
    OLED_Clear();

    // 显示标题
    OLED_ShowString(0, 0, "Voltage Monitor", 16);

    // 显示电压值
    OLED_ShowString(0, 25, "Voltage:", 12);
    OLED_ShowNum(60, 25, voltage_mv/1000, 1, 12);      // 整数部分
    OLED_ShowString(72, 25, ".", 12);                  // 小数点
    OLED_ShowNum(80, 25, (voltage_mv%1000)/10, 2, 12); // 小数部分
    OLED_ShowString(104, 25, "V", 12);                 // 单位

    // 显示时间
    uint8_t hour, minute, second;
    rtc_get_time(NULL, NULL, NULL, &hour, &minute, &second);
    OLED_ShowString(0, 45, "Time:", 12);
    OLED_ShowNum(35, 45, hour, 2, 12);
    OLED_ShowString(47, 45, ":", 12);
    OLED_ShowNum(55, 45, minute, 2, 12);
    OLED_ShowString(67, 45, ":", 12);
    OLED_ShowNum(75, 45, second, 2, 12);

    OLED_Refresh_Gram();
}
```

## 5. 注意事项

### 5.1 显示坐标系统
- X坐标：0-127 (从左到右)
- Y坐标：0-63 (从上到下)
- 原点(0,0)在屏幕左上角

### 5.2 字体大小
- 12号字体：6x12像素，适合显示较多文字
- 16号字体：8x16像素，适合显示标题和重要信息

### 5.3 显示流程
1. 清空显存：`OLED_Clear()`
2. 写入内容：`OLED_ShowString()` 或 `OLED_ShowNum()`
3. 刷新显示：`OLED_Refresh_Gram()`

### 5.4 支持的字符
- 大写字母：A-Z
- 小写字母：a-z
- 数字：0-9
- 基本符号：. , : ; ! ? - + = ( ) [ ] / \ @ # $ % & *

### 5.5 使用建议
- 避免频繁刷新，影响性能
- 合理安排显示内容的位置
- 重要信息使用16号字体显示

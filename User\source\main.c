#include "cmic_gd32f470vet6.h"

int main(void)
{
#ifdef __FIRMWARE_VERSION_DEFINE
    uint32_t fw_ver = 0;
#endif
    systick_config();
    init_cycle_counter(false);
    delay_ms(200); // Wait download if SWIO be set to GPIO
    
#ifdef __FIRMWARE_VERSION_DEFINE
    fw_ver = gd32f4xx_firmware_version_get();
    /* print firmware version */
    //printf("\r\nGD32F4xx series firmware version: V%d.%d.%d", (uint8_t)(fw_ver >> 24), (uint8_t)(fw_ver >> 16), (uint8_t)(fw_ver >> 8));
#endif /* __FIRMWARE_VERSION_DEFINE */
    
    bsp_led_init();
    bsp_btn_init();
    bsp_oled_init();
    bsp_gd25qxx_init();
    bsp_usart_init();
    bsp_rtc_init();
    bsp_adc_init();  // 初始化ADC

    // 初始化USART1 (RS485串口)
    usart1_app_init();

    // 初始化RS485通信模块
    rs485_app_init();

    // System initialization
    delay_ms(100); // Wait for USART to be ready

    // 测试调试串口输出
    my_printf(DEBUG_USART, "\r\n=== System Started ===\r\n");
    my_printf(DEBUG_USART, "USART0 Debug Port: OK\r\n");
    my_printf(DEBUG_USART, "RS485 Module: Initialized\r\n");
    my_printf(DEBUG_USART, "======================\r\n");

    // Check reset reason and handle log system accordingly
    uint32_t reset_flags = RCU_RSTSCK;

    // Clear reset flags
    RCU_RSTSCK |= RCU_RSTSCK_RSTFC;

    // Load pre-test buffer from Flash first
    extern void load_pre_test_buffer_from_flash(void);
    load_pre_test_buffer_from_flash();

    // Check if this is subsequent boot (log system already initialized)
    extern uint8_t is_log_initialized(void);
    if(is_log_initialized())
    {
        // Subsequent boot - clear pre-test buffer to start fresh
        extern void clear_pre_test_buffer(void);
        clear_pre_test_buffer();
    }
    else
    {
        // First boot - keep existing pre-test buffer content (if any)
        // Reset log system for fresh start
        reset_log_system();
    }

    // Now output system init message (will be captured in pre_test_buffer)
    my_printf(DEBUG_USART, "====system init====\r\n");

    // Initialize log system
    log_init();

    // Write Device ID to Flash
    write_device_id_to_flash();

    // Read and display Device ID from Flash
    read_and_display_device_id();

    // Load configuration from Flash on startup
    load_config_from_flash_on_startup();

    my_printf(DEBUG_USART, "====system ready====\r\n");

    sd_fatfs_init();

    // TF card filesystem will be initialized only when 'test' command is executed

    OLED_Init();
    
    scheduler_init();
    while(1) 
		{
       scheduler_run();
    }
}

#ifdef GD_ECLIPSE_GCC
/* retarget the C library printf function to the USART, in Eclipse GCC environment */
int __io_putchar(int ch)
{
    usart_data_transmit(EVAL_COM0, (uint8_t)ch);
    while(RESET == usart_flag_get(EVAL_COM0, USART_FLAG_TBE));
    return ch;
}
#else
/* retarget the C library printf function to the USART */
int fputc(int ch, FILE *f)
{
    usart_data_transmit(USART0, (uint8_t)ch);
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    return ch;
}
#endif /* GD_ECLIPSE_GCC */

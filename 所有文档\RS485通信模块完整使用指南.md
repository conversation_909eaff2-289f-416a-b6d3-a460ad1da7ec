# RS485通信模块完整使用指南

## 概述

本系统提供了完整的RS485通信功能，支持半双工通信、协议解析、错误处理和多设备组网。RS485模块基于USART1硬件接口，使用DMA进行高效的数据传输。

## 硬件连接

### 引脚配置
根据您的板子硬件设计：
- **PA1 (485_CS)** - RS485方向控制引脚
- **PA2 (USART1_TX)** - 发送引脚，连接MAX485的DI
- **PA3 (USART1_RX)** - 接收引脚，连接MAX485的RO
- **外部端子：G, A+, B-** - RS485差分信号线

### 硬件特性
- 支持半双工通信
- 自动方向控制
- DMA传输，减少CPU负担
- 硬件错误检测
- 可配置波特率（默认115200）

## 核心文件结构

### 底层驱动文件
- **Hardware/bsp/bsp_rs485.h** - RS485底层驱动头文件
- **Hardware/bsp/bsp_rs485.c** - RS485底层驱动实现

### 应用层文件
- **sysfunction/rs485_app.h** - RS485应用层头文件
- **sysfunction/rs485_app.c** - RS485应用层实现

## 1. 系统初始化

### 1.1 自动初始化
RS485模块在系统启动时自动初始化：

```c
// 在main.c中自动调用
void main(void) {
    // 其他硬件初始化...
    rs485_app_init();  // RS485应用层初始化
    // 继续其他初始化...
}
```

### 1.2 初始化过程
1. 配置GPIO引脚（PA1, PA2, PA3）
2. 初始化USART1硬件
3. 配置DMA通道
4. 设置中断优先级
5. 启动接收DMA

## 2. 协议格式

### 2.1 帧结构
```
+--------+--------+--------+--------+--------+--------+--------+
| 帧头   | 地址   | 命令   | 长度   | 数据   | 校验   | 帧尾   |
| 0xAA   | ADDR   | CMD    | LEN    | DATA   | CHK    | 0x55   |
+--------+--------+--------+--------+--------+--------+--------+
```

### 2.2 字段说明
- **帧头 (0xAA)**: 固定帧头标识
- **地址**: 目标设备地址 (0x01-0xFE, 0xFF为广播)
- **命令**: 命令字节
- **长度**: 数据字段长度
- **数据**: 实际数据内容
- **校验**: 校验和（取反加1）
- **帧尾 (0x55)**: 固定帧尾标识

### 2.3 支持的命令
```c
typedef enum {
    RS485_CMD_PING = 0x01,          // 心跳检测
    RS485_CMD_READ_DATA = 0x02,     // 读取数据
    RS485_CMD_WRITE_DATA = 0x03,    // 写入数据
    RS485_CMD_GET_STATUS = 0x04,    // 获取状态
    RS485_CMD_SET_CONFIG = 0x05,    // 设置配置
    RS485_CMD_RESET = 0x06,         // 复位命令
    RS485_CMD_ACK = 0x80,           // 应答标志位
    RS485_CMD_NACK = 0x81           // 否定应答
} rs485_cmd_t;
```

## 3. 串口命令接口

### 3.1 基本测试命令

#### 自检测试
```
rs485 test
```
**功能**: 测试RS485模块的基本功能
**输出**: 显示BSP状态、应用状态、本地地址、帧构建测试结果

#### 回环测试
```
rs485 loopback
```
**功能**: 发送测试数据进行回环测试
**输出**: 显示发送状态

#### 统计信息
```
rs485 stats
```
**功能**: 显示RS485通信统计信息
**输出**: 发送/接收帧数、错误计数、最后通信时间

#### 清除统计
```
rs485 clear
```
**功能**: 清除所有统计信息

### 3.2 通信命令

#### 发送心跳包
```
rs485 ping [地址]
```
**示例**: 
- `rs485 ping` - 向默认地址0x02发送心跳
- `rs485 ping 05` - 向地址0x05发送心跳

#### 发送数据
```
rs485 send [地址] [消息]
```
**示例**:
- `rs485 send 02 Hello` - 向地址0x02发送"Hello"
- `rs485 send 03 Test Message` - 向地址0x03发送"Test Message"

### 3.3 配置命令

#### 查看/设置本地地址
```
rs485 addr [新地址]
```
**示例**:
- `rs485 addr` - 查看当前本地地址
- `rs485 addr 01` - 设置本地地址为0x01

## 4. 编程接口

### 4.1 基本发送函数

#### 发送帧
```c
uint8_t rs485_send_frame(uint8_t address, uint8_t command, uint8_t *data, uint8_t length);
```
**参数**:
- address: 目标地址
- command: 命令字
- data: 数据指针
- length: 数据长度
**返回**: 1成功，0失败

#### 发送数据
```c
uint8_t rs485_send_data(uint8_t address, uint8_t *data, uint8_t length);
```

#### 发送心跳
```c
uint8_t rs485_send_ping(uint8_t address);
```

### 4.2 状态查询函数

#### 获取应用状态
```c
rs485_app_state_t rs485_get_app_state(void);
```

#### 获取统计信息
```c
rs485_statistics_t* rs485_get_statistics(void);
```

### 4.3 配置函数

#### 设置本地地址
```c
void rs485_set_local_address(uint8_t address);
```

#### 设置回调函数
```c
void rs485_set_frame_received_callback(rs485_frame_received_callback_t callback);
void rs485_set_error_callback(rs485_error_callback_t callback);
```

## 5. 任务调度

### 5.1 自动调度
RS485任务已集成到系统调度器中，每10ms执行一次：

```c
// 在scheduler.c中自动调用
{rs485_task, 10, 0}    // RS485任务，10毫秒周期
```

### 5.2 任务功能
- 检查接收数据
- 解析协议帧
- 处理超时
- 状态管理
- 错误处理

## 6. 错误处理

### 6.1 错误类型
```c
typedef enum {
    RS485_ERROR_NONE = 0,       // 无错误
    RS485_ERROR_TIMEOUT,        // 超时错误
    RS485_ERROR_OVERRUN,        // 数据溢出
    RS485_ERROR_FRAME,          // 帧错误
    RS485_ERROR_PARITY,         // 校验错误
    RS485_ERROR_DMA             // DMA错误
} rs485_error_t;
```

### 6.2 错误处理机制
- 自动重试（最多3次）
- 错误计数统计
- 错误回调通知
- 自动恢复机制

## 7. 使用示例

### 7.1 基本通信示例
```c
// 发送数据到设备0x02
uint8_t data[] = "Hello World";
if(rs485_send_data(0x02, data, strlen((char*)data))) {
    printf("Data sent successfully\r\n");
} else {
    printf("Send failed\r\n");
}
```

### 7.2 接收处理示例
```c
// 设置接收回调
void my_frame_received_callback(rs485_frame_t *frame) {
    printf("Received from 0x%02X: cmd=0x%02X, len=%d\r\n", 
           frame->address, frame->command, frame->length);
}

// 注册回调
rs485_set_frame_received_callback(my_frame_received_callback);
```

## 8. 调试和故障排除

### 8.1 常见问题

#### 无法发送数据
1. 检查硬件连接
2. 确认485_CS引脚配置
3. 检查波特率设置
4. 查看错误统计

#### 接收不到数据
1. 检查A+/B-线序
2. 确认地址匹配
3. 检查终端电阻
4. 查看帧格式

### 8.2 调试命令
```
rs485 test      # 全面自检
rs485 stats     # 查看统计
rs485 loopback  # 回环测试
```

## 9. 性能特点

- **高效传输**: DMA方式，CPU占用率低
- **可靠通信**: 硬件校验，自动重试
- **灵活配置**: 支持多种波特率和地址
- **实时响应**: 10ms任务周期，快速处理
- **完整协议**: 支持多种命令类型
- **易于扩展**: 模块化设计，便于功能扩展

## 10. 注意事项

1. **硬件连接**: 确保A+/B-正确连接，注意终端电阻
2. **地址管理**: 避免地址冲突，合理分配设备地址
3. **数据长度**: 单帧数据不超过250字节
4. **通信距离**: 根据波特率调整通信距离
5. **电源稳定**: 确保485收发器供电稳定
6. **接地良好**: 保证信号地连接可靠

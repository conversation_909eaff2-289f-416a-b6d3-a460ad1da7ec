#include "bsp_rs485.h"
#include "cmic_gd32f470vet6.h"
#include <string.h>

/* 全局变量定义 */
uint8_t rs485_tx_buffer[RS485_TX_BUFFER_SIZE];
uint8_t rs485_rx_buffer[RS485_RX_BUFFER_SIZE];
volatile rs485_ctrl_t rs485_ctrl;

/* 私有变量 */
static uint16_t rs485_rx_length = 0;
static uint32_t rs485_timeout_counter = 0;

/*!
    \brief      初始化RS485模块
    \param[in]  none
    \param[out] none
    \retval     none
*/
void bsp_rs485_init(void)
{
    dma_single_data_parameter_struct dma_init_struct;
    
    /* 初始化控制结构体 */
    memset((void*)&rs485_ctrl, 0, sizeof(rs485_ctrl_t));
    rs485_ctrl.state = RS485_STATE_IDLE;
    rs485_ctrl.last_error = RS485_ERROR_NONE;
    
    /* 使能时钟 */
    rcu_periph_clock_enable(RS485_GPIO_CLK);
    rcu_periph_clock_enable(RS485_USART_CLK);
    rcu_periph_clock_enable(RS485_DMA_CLK);
    
    /* 配置GPIO引脚 */
    /* TX引脚配置 */
    gpio_af_set(RS485_GPIO_PORT, RS485_TX_AF, RS485_TX_PIN);
    gpio_mode_set(RS485_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, RS485_TX_PIN);
    gpio_output_options_set(RS485_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, RS485_TX_PIN);
    
    /* RX引脚配置 */
    gpio_af_set(RS485_GPIO_PORT, RS485_RX_AF, RS485_RX_PIN);
    gpio_mode_set(RS485_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, RS485_RX_PIN);
    gpio_output_options_set(RS485_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, RS485_RX_PIN);
    
    /* 方向控制引脚配置 */
    gpio_mode_set(RS485_GPIO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, RS485_DIR_PIN);
    gpio_output_options_set(RS485_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, RS485_DIR_PIN);
    
    /* 默认设置为接收模式 */
    RS485_RX_MODE();
    
    /* 配置USART */
    usart_deinit(RS485_USART);
    usart_baudrate_set(RS485_USART, RS485_USART_BAUDRATE);
    usart_word_length_set(RS485_USART, USART_WL_8BIT);
    usart_stop_bit_set(RS485_USART, USART_STB_1BIT);
    usart_parity_config(RS485_USART, USART_PM_NONE);
    usart_hardware_flow_rts_config(RS485_USART, USART_RTS_DISABLE);
    usart_hardware_flow_cts_config(RS485_USART, USART_CTS_DISABLE);
    usart_receive_config(RS485_USART, USART_RECEIVE_ENABLE);
    usart_transmit_config(RS485_USART, USART_TRANSMIT_ENABLE);
    
    /* 配置DMA接收通道 */
    dma_deinit(RS485_DMA, RS485_DMA_CH_RX);
    dma_init_struct.direction = DMA_PERIPH_TO_MEMORY;
    dma_init_struct.memory0_addr = (uint32_t)rs485_rx_buffer;
    dma_init_struct.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.number = RS485_RX_BUFFER_SIZE;
    dma_init_struct.periph_addr = (uint32_t)&USART_DATA(RS485_USART);
    dma_init_struct.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.periph_memory_width = DMA_PERIPH_WIDTH_8BIT;
    dma_init_struct.priority = DMA_PRIORITY_HIGH;
    dma_single_data_mode_init(RS485_DMA, RS485_DMA_CH_RX, &dma_init_struct);
    dma_circulation_disable(RS485_DMA, RS485_DMA_CH_RX);
    dma_channel_subperipheral_select(RS485_DMA, RS485_DMA_CH_RX, RS485_DMA_SUBPERI);
    
    /* 配置DMA发送通道 */
    dma_deinit(RS485_DMA, RS485_DMA_CH_TX);
    dma_init_struct.direction = DMA_MEMORY_TO_PERIPH;
    dma_init_struct.memory0_addr = (uint32_t)rs485_tx_buffer;
    dma_init_struct.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.number = 0;  // 发送时动态设置
    dma_init_struct.periph_addr = (uint32_t)&USART_DATA(RS485_USART);
    dma_init_struct.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.periph_memory_width = DMA_PERIPH_WIDTH_8BIT;
    dma_init_struct.priority = DMA_PRIORITY_HIGH;
    dma_single_data_mode_init(RS485_DMA, RS485_DMA_CH_TX, &dma_init_struct);
    dma_circulation_disable(RS485_DMA, RS485_DMA_CH_TX);
    dma_channel_subperipheral_select(RS485_DMA, RS485_DMA_CH_TX, RS485_DMA_SUBPERI);
    
    /* 使能USART DMA */
    usart_dma_receive_config(RS485_USART, USART_RECEIVE_DMA_ENABLE);
    usart_dma_transmit_config(RS485_USART, USART_TRANSMIT_DMA_ENABLE);
    
    /* 配置中断 */
    nvic_irq_enable(RS485_USART_IRQn, 2, 0);
    nvic_irq_enable(RS485_DMA_RX_IRQn, 2, 1);
    nvic_irq_enable(RS485_DMA_TX_IRQn, 2, 2);
    
    /* 使能USART中断 */
    usart_interrupt_enable(RS485_USART, USART_INT_IDLE);  // 空闲中断用于接收完成检测
    usart_interrupt_enable(RS485_USART, USART_INT_ERR);   // 错误中断
    
    /* 使能DMA中断 */
    dma_interrupt_enable(RS485_DMA, RS485_DMA_CH_RX, DMA_CHXCTL_FTFIE);
    dma_interrupt_enable(RS485_DMA, RS485_DMA_CH_TX, DMA_CHXCTL_FTFIE);
    
    /* 使能USART */
    usart_enable(RS485_USART);
    
    /* 启动接收DMA */
    dma_channel_enable(RS485_DMA, RS485_DMA_CH_RX);
}

/*!
    \brief      反初始化RS485模块
    \param[in]  none
    \param[out] none
    \retval     none
*/
void bsp_rs485_deinit(void)
{
    /* 禁用DMA通道 */
    dma_channel_disable(RS485_DMA, RS485_DMA_CH_RX);
    dma_channel_disable(RS485_DMA, RS485_DMA_CH_TX);
    
    /* 禁用中断 */
    nvic_irq_disable(RS485_USART_IRQn);
    nvic_irq_disable(RS485_DMA_RX_IRQn);
    nvic_irq_disable(RS485_DMA_TX_IRQn);
    
    /* 禁用USART */
    usart_disable(RS485_USART);
    
    /* 重置控制结构体 */
    memset((void*)&rs485_ctrl, 0, sizeof(rs485_ctrl_t));
    rs485_ctrl.state = RS485_STATE_IDLE;
}

/*!
    \brief      发送数据
    \param[in]  data: 要发送的数据指针
    \param[in]  length: 数据长度
    \param[out] none
    \retval     1: 成功, 0: 失败
*/
uint8_t bsp_rs485_send_data(uint8_t *data, uint16_t length)
{
    if (data == NULL || length == 0 || length > RS485_TX_BUFFER_SIZE) {
        return 0;
    }
    
    if (rs485_ctrl.state != RS485_STATE_IDLE) {
        return 0;  // 忙碌状态
    }
    
    /* 复制数据到发送缓冲区 */
    memcpy(rs485_tx_buffer, data, length);
    
    /* 设置状态 */
    rs485_ctrl.state = RS485_STATE_TX_BUSY;
    rs485_ctrl.tx_complete_flag = 0;
    
    /* 切换到发送模式 */
    RS485_TX_MODE();
    
    /* 配置DMA发送 */
    dma_channel_disable(RS485_DMA, RS485_DMA_CH_TX);
    dma_transfer_number_config(RS485_DMA, RS485_DMA_CH_TX, length);
    dma_channel_enable(RS485_DMA, RS485_DMA_CH_TX);
    
    return 1;
}

/*!
    \brief      接收数据
    \param[in]  data: 接收数据缓冲区指针
    \param[in]  max_length: 最大接收长度
    \param[out] received_length: 实际接收长度
    \retval     1: 成功, 0: 失败
*/
uint8_t bsp_rs485_receive_data(uint8_t *data, uint16_t max_length, uint16_t *received_length)
{
    if (data == NULL || received_length == NULL || max_length == 0) {
        return 0;
    }

    if (!rs485_ctrl.rx_complete_flag) {
        *received_length = 0;
        return 0;  // 没有接收到完整数据
    }

    /* 计算实际接收长度 */
    uint16_t actual_length = (rs485_rx_length > max_length) ? max_length : rs485_rx_length;

    /* 复制数据 */
    memcpy(data, rs485_rx_buffer, actual_length);
    *received_length = actual_length;

    /* 清除接收完成标志 */
    rs485_ctrl.rx_complete_flag = 0;
    rs485_rx_length = 0;

    /* 重新启动接收DMA */
    dma_channel_disable(RS485_DMA, RS485_DMA_CH_RX);
    dma_transfer_number_config(RS485_DMA, RS485_DMA_CH_RX, RS485_RX_BUFFER_SIZE);
    dma_channel_enable(RS485_DMA, RS485_DMA_CH_RX);

    return 1;
}

/*!
    \brief      中止传输
    \param[in]  none
    \param[out] none
    \retval     none
*/
void bsp_rs485_abort_transfer(void)
{
    /* 禁用DMA通道 */
    dma_channel_disable(RS485_DMA, RS485_DMA_CH_TX);
    dma_channel_disable(RS485_DMA, RS485_DMA_CH_RX);

    /* 切换到接收模式 */
    RS485_RX_MODE();

    /* 重置状态 */
    rs485_ctrl.state = RS485_STATE_IDLE;
    rs485_ctrl.tx_complete_flag = 0;
    rs485_ctrl.rx_complete_flag = 0;

    /* 重新启动接收DMA */
    dma_transfer_number_config(RS485_DMA, RS485_DMA_CH_RX, RS485_RX_BUFFER_SIZE);
    dma_channel_enable(RS485_DMA, RS485_DMA_CH_RX);
}

/*!
    \brief      获取RS485状态
    \param[in]  none
    \param[out] none
    \retval     rs485_state_t: 当前状态
*/
rs485_state_t bsp_rs485_get_state(void)
{
    return rs485_ctrl.state;
}

/*!
    \brief      获取最后错误
    \param[in]  none
    \param[out] none
    \retval     rs485_error_t: 最后错误类型
*/
rs485_error_t bsp_rs485_get_last_error(void)
{
    return rs485_ctrl.last_error;
}

/*!
    \brief      清除错误
    \param[in]  none
    \param[out] none
    \retval     none
*/
void bsp_rs485_clear_error(void)
{
    rs485_ctrl.last_error = RS485_ERROR_NONE;
    rs485_ctrl.error_count = 0;
}

/*!
    \brief      设置波特率
    \param[in]  baudrate: 波特率
    \param[out] none
    \retval     none
*/
void bsp_rs485_set_baudrate(uint32_t baudrate)
{
    usart_disable(RS485_USART);
    usart_baudrate_set(RS485_USART, baudrate);
    usart_enable(RS485_USART);
}

/*!
    \brief      USART1中断处理函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void USART1_IRQHandler(void)
{
    /* 空闲中断 - 接收完成 */
    if (usart_interrupt_flag_get(RS485_USART, USART_INT_FLAG_IDLE)) {
        usart_interrupt_flag_clear(RS485_USART, USART_INT_FLAG_IDLE);

        /* 计算接收到的数据长度 */
        rs485_rx_length = RS485_RX_BUFFER_SIZE - dma_transfer_number_get(RS485_DMA, RS485_DMA_CH_RX);

        if (rs485_rx_length > 0) {
            rs485_ctrl.rx_complete_flag = 1;
            rs485_ctrl.rx_count++;
            rs485_ctrl.last_activity_time = get_system_ms();
        }

        /* 停止接收DMA */
        dma_channel_disable(RS485_DMA, RS485_DMA_CH_RX);
    }

    /* 错误中断处理 */
    if (usart_interrupt_flag_get(RS485_USART, USART_INT_FLAG_ERR_ORERR)) {
        usart_interrupt_flag_clear(RS485_USART, USART_INT_FLAG_ERR_ORERR);
        rs485_ctrl.last_error = RS485_ERROR_OVERRUN;
        rs485_ctrl.error_count++;
    }

    if (usart_interrupt_flag_get(RS485_USART, USART_INT_FLAG_ERR_FERR)) {
        usart_interrupt_flag_clear(RS485_USART, USART_INT_FLAG_ERR_FERR);
        rs485_ctrl.last_error = RS485_ERROR_FRAME;
        rs485_ctrl.error_count++;
    }

    if (usart_interrupt_flag_get(RS485_USART, USART_INT_FLAG_ERR_PERR)) {
        usart_interrupt_flag_clear(RS485_USART, USART_INT_FLAG_ERR_PERR);
        rs485_ctrl.last_error = RS485_ERROR_PARITY;
        rs485_ctrl.error_count++;
    }
}

/*!
    \brief      DMA1 Channel5中断处理函数 (RS485 RX)
    \param[in]  none
    \param[out] none
    \retval     none
*/
void DMA1_Channel5_IRQHandler(void)
{
    if (dma_interrupt_flag_get(RS485_DMA, RS485_DMA_CH_RX, DMA_INT_FLAG_FTF)) {
        dma_interrupt_flag_clear(RS485_DMA, RS485_DMA_CH_RX, DMA_INT_FLAG_FTF);

        /* 接收缓冲区满 */
        rs485_rx_length = RS485_RX_BUFFER_SIZE;
        rs485_ctrl.rx_complete_flag = 1;
        rs485_ctrl.rx_count++;
        rs485_ctrl.last_activity_time = get_system_ms();
    }
}

/*!
    \brief      DMA1 Channel7中断处理函数 (RS485 TX)
    \param[in]  none
    \param[out] none
    \retval     none
*/
void DMA1_Channel7_IRQHandler(void)
{
    if (dma_interrupt_flag_get(RS485_DMA, RS485_DMA_CH_TX, DMA_INT_FLAG_FTF)) {
        dma_interrupt_flag_clear(RS485_DMA, RS485_DMA_CH_TX, DMA_INT_FLAG_FTF);

        /* 发送完成 */
        rs485_ctrl.tx_complete_flag = 1;
        rs485_ctrl.tx_count++;
        rs485_ctrl.state = RS485_STATE_IDLE;
        rs485_ctrl.last_activity_time = get_system_ms();

        /* 切换回接收模式 */
        RS485_RX_MODE();

        /* 重新启动接收DMA */
        dma_channel_disable(RS485_DMA, RS485_DMA_CH_RX);
        dma_transfer_number_config(RS485_DMA, RS485_DMA_CH_RX, RS485_RX_BUFFER_SIZE);
        dma_channel_enable(RS485_DMA, RS485_DMA_CH_RX);
    }
}

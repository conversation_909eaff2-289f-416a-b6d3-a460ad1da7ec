#ifndef __BSP_RS485_H__
#define __BSP_RS485_H__

#include "gd32f4xx.h"
#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

/* RS485硬件配置宏定义 - 基于您的板子引脚 */
#define RS485_USART                 USART1
#define RS485_USART_CLK             RCU_USART1
#define RS485_USART_BAUDRATE        115200U

/* RS485 GPIO端口配置 */
#define RS485_GPIO_PORT             GPIOA
#define RS485_GPIO_CLK              RCU_GPIOA

/* RS485 TX引脚配置 (PA2) */
#define RS485_TX_PIN                GPIO_PIN_2
#define RS485_TX_AF                 GPIO_AF_7

/* RS485 RX引脚配置 (PA3) */
#define RS485_RX_PIN                GPIO_PIN_3
#define RS485_RX_AF                 GPIO_AF_7

/* RS485方向控制引脚配置 (PA1 - 485_CS) */
#define RS485_DIR_PIN               GPIO_PIN_1

/* DMA配置 */
#define RS485_DMA                   DMA1
#define RS485_DMA_CLK               RCU_DMA1
#define RS485_DMA_CH_TX             DMA_CH7    // USART1_TX对应DMA1_CH7
#define RS485_DMA_CH_RX             DMA_CH5    // USART1_RX对应DMA1_CH5
#define RS485_DMA_SUBPERI           DMA_SUBPERI4

/* 中断配置 */
#define RS485_USART_IRQn            USART1_IRQn
#define RS485_DMA_TX_IRQn           DMA1_Channel7_IRQn
#define RS485_DMA_RX_IRQn           DMA1_Channel5_IRQn

/* 缓冲区大小定义 */
#define RS485_TX_BUFFER_SIZE        512
#define RS485_RX_BUFFER_SIZE        512

/* 超时定义 */
#define RS485_TIMEOUT_MS            1000
#define RS485_BYTE_TIMEOUT_MS       10

/* 方向控制宏 - PA1控制485_CS */
#define RS485_TX_MODE()             gpio_bit_set(RS485_GPIO_PORT, RS485_DIR_PIN)    // 高电平发送
#define RS485_RX_MODE()             gpio_bit_reset(RS485_GPIO_PORT, RS485_DIR_PIN)  // 低电平接收

/* RS485状态枚举 */
typedef enum {
    RS485_STATE_IDLE = 0,
    RS485_STATE_TX_BUSY,
    RS485_STATE_RX_BUSY,
    RS485_STATE_ERROR
} rs485_state_t;

/* RS485错误类型枚举 */
typedef enum {
    RS485_ERROR_NONE = 0,
    RS485_ERROR_TIMEOUT,
    RS485_ERROR_OVERRUN,
    RS485_ERROR_FRAME,
    RS485_ERROR_PARITY,
    RS485_ERROR_DMA
} rs485_error_t;

/* RS485控制结构体 */
typedef struct {
    rs485_state_t state;
    rs485_error_t last_error;
    uint32_t tx_count;
    uint32_t rx_count;
    uint32_t error_count;
    uint8_t tx_complete_flag;
    uint8_t rx_complete_flag;
} rs485_ctrl_t;

/* 全局变量声明 */
extern uint8_t rs485_tx_buffer[RS485_TX_BUFFER_SIZE];
extern uint8_t rs485_rx_buffer[RS485_RX_BUFFER_SIZE];
extern volatile rs485_ctrl_t rs485_ctrl;

/* 函数声明 */
void bsp_rs485_init(void);
void bsp_rs485_deinit(void);
uint8_t bsp_rs485_send_data(uint8_t *data, uint16_t length);
uint8_t bsp_rs485_receive_data(uint8_t *data, uint16_t max_length, uint16_t *received_length);
void bsp_rs485_abort_transfer(void);
rs485_state_t bsp_rs485_get_state(void);
rs485_error_t bsp_rs485_get_last_error(void);
void bsp_rs485_clear_error(void);
void bsp_rs485_set_baudrate(uint32_t baudrate);

/* 中断处理函数声明 */
void USART1_IRQHandler(void);
void DMA1_Channel5_IRQHandler(void);
void DMA1_Channel7_IRQHandler(void);

#ifdef __cplusplus
}
#endif

#endif /* __BSP_RS485_H__ */

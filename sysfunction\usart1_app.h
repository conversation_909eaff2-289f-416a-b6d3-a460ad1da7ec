#ifndef __USART1_APP_H__
#define __USART1_APP_H__

#include "stdint.h"
#include "stdio.h"

#ifdef __cplusplus
extern "C" {
#endif

/* USART1配置宏定义 */
#define USART1_BUFFER_SIZE          512
#define USART1_CMD_BUFFER_SIZE      256
#define USART1_MAX_CMD_LENGTH       128

/* USART1状态枚举 */
typedef enum {
    USART1_STATE_IDLE = 0,
    USART1_STATE_RECEIVING,
    USART1_STATE_TRANSMITTING,
    USART1_STATE_ERROR
} usart1_state_t;

/* USART1统计信息 */
typedef struct {
    uint32_t bytes_sent;
    uint32_t bytes_received;
    uint32_t frames_received;
    uint32_t cmd_processed;
    uint32_t errors;
    uint32_t last_activity_time;
} usart1_statistics_t;

/* 全局变量声明 */
extern uint8_t usart1_dma_buffer[USART1_BUFFER_SIZE];
extern uint8_t usart1_rx_buffer[USART1_BUFFER_SIZE];
extern volatile uint8_t usart1_rx_flag;
extern volatile usart1_state_t usart1_state;
extern usart1_statistics_t usart1_stats;

/* 函数声明 */
void usart1_app_init(void);
void usart1_task(void);

/* 发送函数 */
void usart1_send_char(char ch);
void usart1_send_string(const char* str);
void usart1_send_data(uint8_t* data, uint16_t length);
int usart1_printf(const char* format, ...);

/* 接收函数 */
uint16_t usart1_get_received_length(void);
uint8_t usart1_read_byte(void);
uint16_t usart1_read_data(uint8_t* buffer, uint16_t max_length);
void usart1_clear_rx_buffer(void);

/* 命令处理函数 */
void usart1_process_command(char* cmd_str);
void usart1_process_received_data(uint8_t* data, uint16_t length);
void usart1_send_response(const char* response);

/* 状态和统计函数 */
usart1_state_t usart1_get_state(void);
usart1_statistics_t* usart1_get_statistics(void);
void usart1_clear_statistics(void);
void usart1_print_statistics(void);

/* 配置函数 */
void usart1_set_baudrate(uint32_t baudrate);
void usart1_enable_echo(uint8_t enable);

/* 测试函数 */
void usart1_self_test(void);
void usart1_loopback_test(void);

#ifdef __cplusplus
}
#endif

#endif /* __USART1_APP_H__ */

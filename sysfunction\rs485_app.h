#ifndef __RS485_APP_H__
#define __RS485_APP_H__

#include "stdint.h"
#include "stdio.h"

#ifdef __cplusplus
extern "C" {
#endif

/* RS485应用层配置 */
#define RS485_MAX_FRAME_SIZE        256
#define RS485_TIMEOUT_MS            1000
#define RS485_RETRY_COUNT           3

/* RS485协议定义 */
#define RS485_FRAME_HEADER          0xAA
#define RS485_FRAME_TAIL            0x55
#define RS485_MIN_FRAME_SIZE        6    // 帧头+地址+命令+长度+校验+帧尾

/* RS485设备地址 */
#define RS485_LOCAL_ADDRESS         0x01
#define RS485_BROADCAST_ADDRESS     0xFF

/* RS485命令定义 */
typedef enum {
    RS485_CMD_PING = 0x01,              // 心跳检测
    RS485_CMD_READ_DATA = 0x02,         // 读取数据
    RS485_CMD_WRITE_DATA = 0x03,        // 写入数据
    RS485_CMD_GET_STATUS = 0x04,        // 获取状态
    RS485_CMD_SET_CONFIG = 0x05,        // 设置配置
    RS485_CMD_RESET = 0x06,             // 复位命令
    RS485_CMD_ACK = 0x80,               // 应答标志位
    RS485_CMD_NACK = 0x81               // 否定应答
} rs485_cmd_t;

/* RS485帧结构体 */
typedef struct {
    uint8_t header;                     // 帧头 0xAA
    uint8_t address;                    // 目标地址
    uint8_t command;                    // 命令字
    uint8_t length;                     // 数据长度
    uint8_t data[RS485_MAX_FRAME_SIZE]; // 数据内容
    uint8_t checksum;                   // 校验和
    uint8_t tail;                       // 帧尾 0x55
} rs485_frame_t;

/* RS485应用状态 */
typedef enum {
    RS485_APP_STATE_IDLE = 0,
    RS485_APP_STATE_SENDING,
    RS485_APP_STATE_WAITING_RESPONSE,
    RS485_APP_STATE_RECEIVING,
    RS485_APP_STATE_ERROR
} rs485_app_state_t;

/* RS485统计信息 */
typedef struct {
    uint32_t frames_sent;
    uint32_t frames_received;
    uint32_t frames_error;
    uint32_t checksum_error;
    uint32_t timeout_error;
    uint32_t last_communication_time;
} rs485_statistics_t;

/* 回调函数类型定义 */
typedef void (*rs485_frame_received_callback_t)(rs485_frame_t *frame);
typedef void (*rs485_error_callback_t)(uint8_t error_code);

/* 全局变量声明 */
extern volatile rs485_app_state_t rs485_app_state;
extern rs485_statistics_t rs485_stats;

/* 应用层函数声明 */
void rs485_app_init(void);
void rs485_task(void);
uint8_t rs485_send_frame(uint8_t address, uint8_t command, uint8_t *data, uint8_t length);
uint8_t rs485_send_data(uint8_t address, uint8_t *data, uint8_t length);
uint8_t rs485_send_ping(uint8_t address);
uint8_t rs485_send_ack(uint8_t address, uint8_t original_command);
uint8_t rs485_send_nack(uint8_t address, uint8_t original_command, uint8_t error_code);

/* 协议处理函数 */
uint8_t rs485_parse_frame(uint8_t *buffer, uint16_t length, rs485_frame_t *frame);
uint8_t rs485_build_frame(rs485_frame_t *frame, uint8_t *buffer, uint16_t *length);
uint8_t rs485_calculate_checksum(uint8_t *data, uint16_t length);
uint8_t rs485_verify_frame(rs485_frame_t *frame);
void rs485_process_received_frame(rs485_frame_t *frame);

/* 配置函数 */
void rs485_set_local_address(uint8_t address);
uint8_t rs485_get_local_address(void);
void rs485_set_frame_received_callback(rs485_frame_received_callback_t callback);
void rs485_set_error_callback(rs485_error_callback_t callback);

/* 状态和统计函数 */
rs485_app_state_t rs485_get_app_state(void);
rs485_statistics_t* rs485_get_statistics(void);
void rs485_clear_statistics(void);
void rs485_print_statistics(void);

/* 测试和调试函数 */
void rs485_self_test(void);
void rs485_loopback_test(void);
void rs485_communication_test(uint8_t target_address);

#ifdef __cplusplus
}
#endif

#endif /* __RS485_APP_H__ */

# RTC模块使用指南

## 概述

本系统提供RTC实时时钟功能，主要用于时间设置、读取和日志时间戳。由于有备份电池，掉电后时间不会丢失。

## 核心文件和函数位置

### 主要文件
- **bsp_rtc.c** - RTC底层驱动实现
- **bsp_rtc.h** - RTC底层驱动头文件
- **usart_app.c** - RTC时间设置和读取
- **log_app.c** - 日志时间戳生成

## 1. RTC初始化

### 1.1 RTC硬件初始化
```c
// 位置：bsp_rtc.c
void bsp_rtc_init(void)
```
**作用**：初始化RTC硬件和时钟源

**调用位置**：main.c中的系统初始化
```c
void main(void) {
    // 其他硬件初始化...
    bsp_rtc_init();  // RTC初始化
    // 继续其他初始化...
}
```

**说明**：由于有备份电池，RTC初始化后会自动运行，掉电不丢失时间

## 2. RTC Config - 时间设置

### 2.1 设置RTC时间
```c
// 位置：bsp_rtc.c
void rtc_set_time(uint8_t year, uint8_t month, uint8_t day,
                  uint8_t hour, uint8_t minute, uint8_t second)
```
**作用**：设置RTC的日期和时间

**参数说明**：
- `year`: 年份 (0-99, 表示2000-2099)
- `month`: 月份 (1-12)
- `day`: 日期 (1-31)
- `hour`: 小时 (0-23)
- `minute`: 分钟 (0-59)
- `second`: 秒钟 (0-59)

**调用示例**：
```c
// 设置时间为2025年8月4日 14:30:25
rtc_set_time(25, 8, 4, 14, 30, 25);
```

### 2.2 串口命令设置时间
```c
// 位置：usart_app.c
uint8_t parse_and_set_rtc_time(char* time_str)
```
**作用**：通过串口命令设置时间

**输入格式**：`YYYY MM DD HH:MM:SS`

**使用方法**：
1. 串口输入任意时间格式，系统提示输入格式
2. 按格式输入：`2025 08 04 14:30:25`
3. 系统自动解析并设置RTC时间

**调用示例**：
```c
if(parse_and_set_rtc_time("2025 08 04 14:30:25")) {
    my_printf(DEBUG_USART, "RTC Config success\r\n");
} else {
    my_printf(DEBUG_USART, "RTC Config failed\r\n");
}
```

## 3. RTC Now - 时间读取

### 3.1 读取当前RTC时间
```c
// 位置：bsp_rtc.c
void rtc_get_time(uint8_t* year, uint8_t* month, uint8_t* day,
                  uint8_t* hour, uint8_t* minute, uint8_t* second)
```
**作用**：读取当前RTC时间到变量中

**时间存储变量**：
```c
uint8_t current_year;    // 年份 (0-99, 表示2000-2099)
uint8_t current_month;   // 月份 (1-12)
uint8_t current_day;     // 日期 (1-31)
uint8_t current_hour;    // 小时 (0-23)
uint8_t current_minute;  // 分钟 (0-59)
uint8_t current_second;  // 秒钟 (0-59)
```

**调用示例**：
```c
// 读取当前时间
uint8_t year, month, day, hour, minute, second;
rtc_get_time(&year, &month, &day, &hour, &minute, &second);

// 显示当前时间
my_printf(DEBUG_USART, "Current Time 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
          year, month, day, hour, minute, second);
```

### 3.2 获取格式化时间字符串
```c
// 位置：log_app.c
void get_timestamp_string(char* buffer, uint8_t buffer_size)
```
**作用**：获取用于日志的时间戳字符串

**调用示例**：
```c
char time_str[32];
get_timestamp_string(time_str, sizeof(time_str));
// 结果：time_str = "2025-08-04 14:30:25"
```

### 3.3 时间戳用于日志
```c
// 在日志系统中自动添加时间戳
void log_write_formatted(const char* format, ...) {
    char timestamp[32];
    get_timestamp_string(timestamp, sizeof(timestamp));

    // 自动在日志前添加时间戳
    // 格式：2025-08-04 14:30:25 日志内容
}
```

## 4. 实际使用示例

### 4.1 系统启动时显示时间
```c
// 在main.c中，系统启动后显示当前时间
void show_current_time(void) {
    uint8_t year, month, day, hour, minute, second;
    rtc_get_time(&year, &month, &day, &hour, &minute, &second);

    my_printf(DEBUG_USART, "Current Time 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
              year, month, day, hour, minute, second);
}
```

### 4.2 日志系统自动添加时间戳
```c
// 日志系统会自动调用RTC获取时间戳
// 每条日志前都会自动添加时间，格式如下：
// 2025-08-04 14:30:25 System started
// 2025-08-04 14:30:26 Test completed
```

### 4.3 串口命令设置时间
```c
// 用户通过串口输入时间格式，系统会提示：
// Input Datatime
// Format: YYYY MM DD HH:MM:SS
// Example: 2025 06 15 10:10:10

// 用户输入：2025 08 04 14:30:25
// 系统响应：RTC Config success
//          Time:2025-08-04 14:30:25
```

## 5. 注意事项

### 5.1 时间变量类型
```c
// RTC时间使用uint8_t类型存储
uint8_t year;    // 0-99 (表示2000-2099年)
uint8_t month;   // 1-12
uint8_t day;     // 1-31
uint8_t hour;    // 0-23
uint8_t minute;  // 0-59
uint8_t second;  // 0-59
```

### 5.2 时间格式说明
- **设置时间格式**：`YYYY MM DD HH:MM:SS`
- **显示时间格式**：`YYYY-MM-DD HH:MM:SS`
- **日志时间戳格式**：`YYYY-MM-DD HH:MM:SS 日志内容`

### 5.3 备份电池说明
- 系统有备份电池，掉电后RTC继续运行
- 重新上电后时间不会丢失
- 无需复杂的掉电检测和时间恢复机制

```

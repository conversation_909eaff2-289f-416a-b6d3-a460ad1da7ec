# Flash模块完整使用指南

## 概述

本系统提供了完整的外部Flash存储功能，支持数据读写、扇区擦除、配置保存等。Flash主要用于存储系统配置、日志缓冲区、设备信息等持久化数据。

## 核心文件和函数位置

### 主要文件
- **bsp_gd25qxx.c** - Flash底层驱动实现
- **bsp_gd25qxx.h** - Flash底层驱动头文件
- **usart_app.c** - Flash应用层实现（配置管理）
- **log_app.c** - 日志系统Flash存储
- **main.c** - Flash初始化调用

## 1. Flash初始化

### 1.1 硬件初始化
```c
// 位置：bsp_gd25qxx.c
void bsp_gd25qxx_init(void)
```
**作用**：初始化SPI接口和Flash芯片通信

**调用位置**：main.c中的系统初始化阶段
```c
void main(void) {
    // 其他硬件初始化...
    bsp_gd25qxx_init();  // Flash硬件初始化
    // 继续其他初始化...
}
```

**初始化流程**：
1. 配置SPI接口
2. 配置CS、WP、HOLD等控制引脚
3. 检测Flash芯片ID
4. 设置Flash工作模式

### 1.2 Flash芯片检测
```c
// 位置：bsp_gd25qxx.c
uint32_t spi_flash_read_id(void)
```
**作用**：读取Flash芯片ID，验证通信是否正常

**调用示例**：
```c
uint32_t flash_id = spi_flash_read_id();
if(flash_id == 0xC84013) {  // GD25Q40芯片ID
    my_printf(DEBUG_USART, "Flash detected: GD25Q40\r\n");
} else {
    my_printf(DEBUG_USART, "Flash detection failed: 0x%06lX\r\n", flash_id);
}
```

**常见芯片ID**：
- GD25Q40: 0xC84013
- GD25Q80: 0xC84014
- GD25Q16: 0xC84015

## 2. 基础读写操作

### 2.1 页写入操作
```c
// 位置：bsp_gd25qxx.c
void spi_flash_page_write(uint8_t* pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite)
```
**作用**：向Flash页写入数据（最大256字节）

**参数说明**：
- `pBuffer`: 要写入的数据缓冲区
- `WriteAddr`: 写入地址
- `NumByteToWrite`: 写入字节数（≤256）

**调用示例**：
```c
uint8_t write_data[256] = "Hello Flash World!";
spi_flash_page_write(write_data, 0x1000, strlen((char*)write_data));
```

**注意事项**：
- 写入前必须先擦除对应扇区
- 单次写入不能跨页边界
- 写入地址必须对齐

### 2.2 缓冲区写入操作
```c
// 位置：bsp_gd25qxx.c
void spi_flash_buffer_write(uint8_t* pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite)
```
**作用**：写入任意长度数据，自动处理跨页写入

**调用示例**：
```c
// 写入大量数据
uint8_t large_data[1024];
for(int i = 0; i < 1024; i++) {
    large_data[i] = i & 0xFF;
}
spi_flash_buffer_write(large_data, 0x2000, 1024);
```

### 2.3 数据读取操作
```c
// 位置：bsp_gd25qxx.c
void spi_flash_buffer_read(uint8_t* pBuffer, uint32_t ReadAddr, uint16_t NumByteToRead)
```
**作用**：从Flash读取指定长度的数据

**调用示例**：
```c
uint8_t read_buffer[256];
spi_flash_buffer_read(read_buffer, 0x1000, 256);

// 验证读取的数据
my_printf(DEBUG_USART, "Read data: %s\r\n", read_buffer);
```

### 2.4 扇区擦除操作
```c
// 位置：bsp_gd25qxx.c
void spi_flash_sector_erase(uint32_t SectorAddr)
```
**作用**：擦除4KB扇区（写入前必须先擦除）

**调用示例**：
```c
// 擦除扇区后写入数据
spi_flash_sector_erase(0x1000);  // 擦除0x1000-0x1FFF扇区
delay_ms(100);  // 等待擦除完成

uint8_t new_data[] = "New data after erase";
spi_flash_buffer_write(new_data, 0x1000, strlen((char*)new_data));
```

## 3. 系统配置存储

### 3.1 配置数据结构
```c
// 位置：usart_app.c
typedef struct {
    uint32_t ratio;           // 比例参数 (实际值*100)
    uint32_t limit;           // 限值参数 (实际值*100)
    uint32_t sampling_cycle;  // 采样周期 (5/10/15秒)
    uint32_t magic;           // 魔数验证
} config_data_t;

#define CONFIG_MAGIC 0x12345678
#define CONFIG_FLASH_ADDR 0x00010000  // 配置存储地址
```

### 3.2 配置写入Flash
```c
// 位置：usart_app.c
uint8_t write_config_to_flash(uint32_t ratio, uint32_t limit)
```
**作用**：将配置参数写入Flash

**调用示例**：
```c
// 保存新的配置参数
uint32_t new_ratio = 199;   // 1.99
uint32_t new_limit = 1050;  // 10.50
if(write_config_to_flash(new_ratio, new_limit)) {
    my_printf(DEBUG_USART, "Config saved to Flash\r\n");
} else {
    my_printf(DEBUG_USART, "Config save failed\r\n");
}
```

**内部流程**：
1. 读取现有配置（保留采样周期）
2. 更新比例和限值参数
3. 设置魔数验证
4. 擦除配置扇区
5. 写入新配置数据

### 3.3 配置从Flash读取
```c
// 位置：usart_app.c
uint8_t load_config_from_flash_on_startup(void)
```
**作用**：系统启动时从Flash加载配置

**调用位置**：main.c中的系统初始化阶段
```c
void main(void) {
    // 硬件初始化完成后...
    load_config_from_flash_on_startup();
}
```

**加载流程**：
1. 从Flash读取配置数据
2. 验证魔数是否正确
3. 如果有效，加载到全局变量
4. 如果无效，使用默认配置

### 3.4 采样周期单独管理
```c
// 位置：usart_app.c
uint8_t write_sampling_cycle_to_flash(uint8_t cycle_seconds)
uint8_t read_sampling_cycle_from_flash(void)
```

**调用示例**：
```c
// 设置采样周期为10秒
write_sampling_cycle_to_flash(10);

// 读取采样周期
uint8_t cycle = read_sampling_cycle_from_flash();
my_printf(DEBUG_USART, "Sampling cycle: %d seconds\r\n", cycle);
```

## 4. 设备信息存储

### 4.1 设备ID数据结构
```c
// 位置：usart_app.c
typedef struct {
    char device_id[32];  // 设备ID字符串
    uint32_t magic;      // 魔数验证
} device_id_t;

#define DEVICE_ID_MAGIC 0x87654321
#define DEVICE_ID_FLASH_ADDR 0x00020000  // 设备ID存储地址
```

### 4.2 设备ID写入
```c
// 位置：usart_app.c
uint8_t write_device_id_to_flash(void)
```
**作用**：将设备ID写入Flash

**调用示例**：
```c
// 在系统初始化时写入设备ID
if(write_device_id_to_flash()) {
    my_printf(DEBUG_USART, "Device ID written to Flash\r\n");
}
```

### 4.3 设备ID读取
```c
// 位置：usart_app.c
uint8_t read_and_display_device_id(void)
```
**作用**：从Flash读取并显示设备ID

**调用位置**：main.c中的系统初始化阶段

## 5. 日志系统Flash存储

### 5.1 日志配置结构
```c
// 位置：log_app.c
typedef struct {
    uint32_t boot_count;    // 启动计数
    uint8_t initialized;    // 初始化标志
    uint32_t magic;         // 魔数验证
} log_config_t;

#define LOG_CONFIG_MAGIC 0xABCDEF00
#define LOG_CONFIG_FLASH_ADDR 0x00030000
```

### 5.2 日志配置管理
```c
// 位置：log_app.c
uint8_t write_log_config_to_flash(log_config_t* config)
uint8_t read_log_config_from_flash(log_config_t* config)
```

**调用示例**：
```c
log_config_t config;

// 读取日志配置
if(read_log_config_from_flash(&config)) {
    my_printf(DEBUG_USART, "Boot count: %lu\r\n", config.boot_count);
} else {
    // 初始化默认配置
    config.boot_count = 0;
    config.initialized = 0;
    config.magic = LOG_CONFIG_MAGIC;
    write_log_config_to_flash(&config);
}
```

### 5.3 Pre-test Buffer存储
```c
// 位置：usart_app.c
typedef struct {
    uint32_t magic;                           // 魔数验证
    uint16_t buffer_pos;                      // 当前缓冲区位置
    uint8_t logging_enabled;                  // 日志记录使能标志
    uint8_t reserved;                         // 保留字节
    char buffer_data[PRE_TEST_BUFFER_SIZE];   // 缓冲区数据
} pre_test_buffer_t;

#define PRE_TEST_BUFFER_FLASH_ADDR 0x00050000
#define PRE_TEST_BUFFER_MAGIC 0x12345678
```

**管理函数**：
```c
// 位置：usart_app.c
void save_pre_test_buffer_to_flash(void)
void load_pre_test_buffer_from_flash(void)
void clear_pre_test_buffer(void)
```

## 6. Flash测试和验证

### 6.1 Flash功能测试
```c
// 位置：usart_app.c
uint8_t test_flash(void)
```
**作用**：测试Flash基本功能

**调用示例**：
```c
if(test_flash()) {
    my_printf(DEBUG_USART, "Flash test passed\r\n");
} else {
    my_printf(DEBUG_USART, "Flash test failed\r\n");
}
```

### 6.2 Flash读写验证
```c
// 自定义测试函数
uint8_t flash_read_write_test(void) {
    uint8_t test_data[256];
    uint8_t read_data[256];
    
    // 准备测试数据
    for(int i = 0; i < 256; i++) {
        test_data[i] = i & 0xFF;
    }
    
    // 擦除测试扇区
    spi_flash_sector_erase(0x10000);
    delay_ms(100);
    
    // 写入测试数据
    spi_flash_buffer_write(test_data, 0x10000, 256);
    
    // 读取数据
    spi_flash_buffer_read(read_data, 0x10000, 256);
    
    // 验证数据
    for(int i = 0; i < 256; i++) {
        if(test_data[i] != read_data[i]) {
            my_printf(DEBUG_USART, "Flash test failed at byte %d\r\n", i);
            return 0;
        }
    }
    
    my_printf(DEBUG_USART, "Flash read/write test passed\r\n");
    return 1;
}
```

### 6.3 Flash扇区测试
```c
// 测试多个扇区
uint8_t flash_sector_test(void) {
    uint32_t test_addresses[] = {0x1000, 0x2000, 0x3000, 0x4000};
    uint8_t test_pattern = 0xAA;
    uint8_t read_data;
    
    for(int i = 0; i < 4; i++) {
        // 擦除扇区
        spi_flash_sector_erase(test_addresses[i]);
        delay_ms(100);
        
        // 写入测试模式
        spi_flash_buffer_write(&test_pattern, test_addresses[i], 1);
        
        // 读取验证
        spi_flash_buffer_read(&read_data, test_addresses[i], 1);
        
        if(read_data != test_pattern) {
            my_printf(DEBUG_USART, "Sector test failed at 0x%08lX\r\n", test_addresses[i]);
            return 0;
        }
    }
    
    my_printf(DEBUG_USART, "All sectors test passed\r\n");
    return 1;
}
```

## 7. Flash地址映射管理

### 7.1 地址分配表
```c
// Flash地址分配
#define FLASH_SIZE              0x80000   // 512KB总容量

// 系统配置区域
#define CONFIG_FLASH_ADDR       0x00010000  // 64KB: 系统配置
#define DEVICE_ID_FLASH_ADDR    0x00020000  // 64KB: 设备信息
#define LOG_CONFIG_FLASH_ADDR   0x00030000  // 64KB: 日志配置
#define RESERVED_FLASH_ADDR     0x00040000  // 64KB: 保留区域
#define PRE_TEST_BUFFER_FLASH_ADDR 0x00050000  // 64KB: Pre-test缓冲区

// 用户数据区域
#define USER_DATA_START_ADDR    0x00060000  // 128KB: 用户数据区
#define USER_DATA_END_ADDR      0x0007FFFF
```

### 7.2 地址管理函数
```c
// 检查地址是否有效
uint8_t is_flash_address_valid(uint32_t address, uint32_t size) {
    if(address >= FLASH_SIZE) return 0;
    if(address + size > FLASH_SIZE) return 0;
    return 1;
}

// 获取扇区起始地址
uint32_t get_sector_start_address(uint32_t address) {
    return address & 0xFFFFF000;  // 4KB对齐
}

// 检查是否需要跨扇区操作
uint8_t is_cross_sector_operation(uint32_t address, uint32_t size) {
    uint32_t start_sector = address / 0x1000;
    uint32_t end_sector = (address + size - 1) / 0x1000;
    return (start_sector != end_sector);
}
```

### 7.3 安全写入函数
```c
// 安全的Flash写入（自动处理扇区擦除）
uint8_t safe_flash_write(uint32_t address, uint8_t* data, uint32_t size) {
    // 检查地址有效性
    if(!is_flash_address_valid(address, size)) {
        my_printf(DEBUG_USART, "Invalid flash address\r\n");
        return 0;
    }
    
    // 如果跨扇区，需要特殊处理
    if(is_cross_sector_operation(address, size)) {
        my_printf(DEBUG_USART, "Cross-sector write not supported\r\n");
        return 0;
    }
    
    // 擦除扇区
    uint32_t sector_addr = get_sector_start_address(address);
    spi_flash_sector_erase(sector_addr);
    delay_ms(100);
    
    // 写入数据
    spi_flash_buffer_write(data, address, size);
    
    // 验证写入
    uint8_t* verify_buffer = malloc(size);
    if(verify_buffer == NULL) return 0;
    
    spi_flash_buffer_read(verify_buffer, address, size);
    uint8_t result = (memcmp(data, verify_buffer, size) == 0);
    
    free(verify_buffer);
    return result;
}
```

## 8. 性能优化

### 8.1 批量操作优化
```c
// 批量写入多个配置项
typedef struct {
    uint32_t address;
    uint8_t* data;
    uint32_t size;
} flash_write_item_t;

uint8_t batch_flash_write(flash_write_item_t* items, uint8_t count) {
    for(uint8_t i = 0; i < count; i++) {
        if(!safe_flash_write(items[i].address, items[i].data, items[i].size)) {
            my_printf(DEBUG_USART, "Batch write failed at item %d\r\n", i);
            return 0;
        }
    }
    return 1;
}

// 使用示例
void save_all_config_to_flash(void) {
    config_data_t config = {current_ratio, current_limit, 5, CONFIG_MAGIC};
    device_id_t device = {"2025-CMIC-2025916750", DEVICE_ID_MAGIC};
    
    flash_write_item_t items[] = {
        {CONFIG_FLASH_ADDR, (uint8_t*)&config, sizeof(config)},
        {DEVICE_ID_FLASH_ADDR, (uint8_t*)&device, sizeof(device)}
    };
    
    batch_flash_write(items, 2);
}
```

### 8.2 缓存机制
```c
// Flash读取缓存
#define CACHE_SIZE 256
static struct {
    uint32_t address;
    uint8_t data[CACHE_SIZE];
    uint8_t valid;
} flash_cache;

uint8_t cached_flash_read(uint32_t address, uint8_t* buffer, uint32_t size) {
    // 检查缓存命中
    if(flash_cache.valid && 
       address >= flash_cache.address && 
       address + size <= flash_cache.address + CACHE_SIZE) {
        
        uint32_t offset = address - flash_cache.address;
        memcpy(buffer, &flash_cache.data[offset], size);
        return 1;
    }
    
    // 缓存未命中，从Flash读取
    if(size <= CACHE_SIZE) {
        spi_flash_buffer_read(flash_cache.data, address, CACHE_SIZE);
        flash_cache.address = address;
        flash_cache.valid = 1;
        memcpy(buffer, flash_cache.data, size);
    } else {
        spi_flash_buffer_read(buffer, address, size);
    }
    
    return 1;
}
```

## 9. 故障排除

### 9.1 常见问题及解决方案

**问题1：Flash读取ID失败**
- 检查SPI接口连接是否正确
- 验证时钟频率是否合适
- 确认CS引脚控制是否正常

**问题2：数据写入失败**
- 检查是否先进行了扇区擦除
- 验证写入地址是否有效
- 确认写入数据长度是否超限

**问题3：数据读取错误**
- 检查读取地址是否正确
- 验证Flash是否已正确初始化
- 确认数据是否已正确写入

### 9.2 调试工具函数
```c
// Flash状态检查
void flash_status_check(void) {
    my_printf(DEBUG_USART, "=== Flash Status ===\r\n");

    // 读取Flash ID
    uint32_t flash_id = spi_flash_read_id();
    my_printf(DEBUG_USART, "Flash ID: 0x%06lX\r\n", flash_id);

    // 检查各配置区域
    config_data_t config;
    spi_flash_buffer_read((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config));
    my_printf(DEBUG_USART, "Config Magic: 0x%08lX %s\r\n",
              config.magic, (config.magic == CONFIG_MAGIC) ? "(Valid)" : "(Invalid)");

    device_id_t device;
    spi_flash_buffer_read((uint8_t*)&device, DEVICE_ID_FLASH_ADDR, sizeof(device));
    my_printf(DEBUG_USART, "Device Magic: 0x%08lX %s\r\n",
              device.magic, (device.magic == DEVICE_ID_MAGIC) ? "(Valid)" : "(Invalid)");

    my_printf(DEBUG_USART, "==================\r\n");
}

// Flash内容十六进制显示
void flash_hex_dump(uint32_t address, uint32_t size) {
    uint8_t buffer[16];
    uint32_t remaining = size;
    uint32_t current_addr = address;

    my_printf(DEBUG_USART, "Flash Hex Dump (0x%08lX, %lu bytes):\r\n", address, size);

    while(remaining > 0) {
        uint32_t read_size = (remaining > 16) ? 16 : remaining;
        spi_flash_buffer_read(buffer, current_addr, read_size);

        my_printf(DEBUG_USART, "%08lX: ", current_addr);
        for(uint32_t i = 0; i < read_size; i++) {
            my_printf(DEBUG_USART, "%02X ", buffer[i]);
        }

        // 填充空格
        for(uint32_t i = read_size; i < 16; i++) {
            my_printf(DEBUG_USART, "   ");
        }

        // 显示ASCII字符
        my_printf(DEBUG_USART, " |");
        for(uint32_t i = 0; i < read_size; i++) {
            char c = (buffer[i] >= 32 && buffer[i] <= 126) ? buffer[i] : '.';
            my_printf(DEBUG_USART, "%c", c);
        }
        my_printf(DEBUG_USART, "|\r\n");

        current_addr += read_size;
        remaining -= read_size;
    }
}
```

## 10. 最佳实践

### 10.1 数据完整性保护
```c
// CRC32校验
uint32_t calculate_crc32(uint8_t* data, uint32_t size) {
    uint32_t crc = 0xFFFFFFFF;

    for(uint32_t i = 0; i < size; i++) {
        crc ^= data[i];
        for(int j = 0; j < 8; j++) {
            if(crc & 1) {
                crc = (crc >> 1) ^ 0xEDB88320;
            } else {
                crc >>= 1;
            }
        }
    }

    return ~crc;
}

// 带校验的数据写入
uint8_t secure_flash_write(uint32_t address, uint8_t* data, uint32_t size) {
    uint32_t crc = calculate_crc32(data, size);

    // 写入数据
    if(!safe_flash_write(address, data, size)) return 0;

    // 写入CRC校验
    if(!safe_flash_write(address + size, (uint8_t*)&crc, 4)) return 0;

    return 1;
}
```

这份Flash模块使用指南提供了完整的Flash操作说明，包括基础读写、配置管理、故障排除等各个方面。

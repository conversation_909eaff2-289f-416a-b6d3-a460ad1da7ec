#include "rs485_app.h"
#include "cmic_gd32f470vet6.h"
#include "usart_app.h"
#include <string.h>

/* 全局变量定义 */
volatile rs485_app_state_t rs485_app_state = RS485_APP_STATE_IDLE;
rs485_statistics_t rs485_stats;

/* 私有变量 */
static uint8_t rs485_local_address = RS485_LOCAL_ADDRESS;
static uint8_t rs485_app_rx_buffer[512];  // 应用层接收缓冲区，匹配BSP层大小
static uint8_t rs485_app_tx_buffer[512];  // 应用层发送缓冲区，匹配BSP层大小
static uint32_t rs485_timeout_start = 0;
static uint8_t rs485_retry_count = 0;

/* 回调函数指针 */
static rs485_frame_received_callback_t frame_received_callback = NULL;
static rs485_error_callback_t error_callback = NULL;

/*!
    \brief      初始化RS485应用层
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rs485_app_init(void)
{
    /* 初始化底层驱动 */
    bsp_rs485_init();
    
    /* 初始化应用状态 */
    rs485_app_state = RS485_APP_STATE_IDLE;
    
    /* 清零统计信息 */
    memset(&rs485_stats, 0, sizeof(rs485_statistics_t));
    
    /* 清零缓冲区 */
    memset(rs485_app_rx_buffer, 0, sizeof(rs485_app_rx_buffer));
    memset(rs485_app_tx_buffer, 0, sizeof(rs485_app_tx_buffer));
    
    my_printf(RS485_USART, "RS485 application initialized\r\n");
}

/*!
    \brief      RS485任务函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rs485_task(void)
{
    uint16_t received_length = 0;
    rs485_frame_t frame;
    
    switch (rs485_app_state) {
        case RS485_APP_STATE_IDLE:
            /* 检查是否有数据接收 */
            if (bsp_rs485_receive_data(rs485_app_rx_buffer, sizeof(rs485_app_rx_buffer), &received_length)) {
                if (received_length > 0) {
                    rs485_app_state = RS485_APP_STATE_RECEIVING;
                    rs485_stats.last_communication_time = get_system_ms();
                }
            }
            break;
            
        case RS485_APP_STATE_RECEIVING:
            /* 解析接收到的帧 */
            if (rs485_parse_frame(rs485_app_rx_buffer, received_length, &frame)) {
                /* 验证帧的完整性 */
                if (rs485_verify_frame(&frame)) {
                    rs485_stats.frames_received++;
                    
                    /* 检查地址是否匹配 */
                    if (frame.address == rs485_local_address || frame.address == RS485_BROADCAST_ADDRESS) {
                        /* 调用回调函数处理接收到的帧 */
                        if (frame_received_callback != NULL) {
                            frame_received_callback(&frame);
                        }
                        
                        /* 处理标准命令 */
                        rs485_process_received_frame(&frame);
                    }
                } else {
                    rs485_stats.checksum_error++;
                    rs485_stats.frames_error++;
                }
            } else {
                rs485_stats.frames_error++;
            }
            
            rs485_app_state = RS485_APP_STATE_IDLE;
            break;
            
        case RS485_APP_STATE_SENDING:
            /* 检查发送是否完成 */
            if (bsp_rs485_get_state() == RS485_STATE_IDLE) {
                rs485_stats.frames_sent++;
                rs485_app_state = RS485_APP_STATE_IDLE;
            }
            break;
            
        case RS485_APP_STATE_WAITING_RESPONSE:
            /* 检查超时 */
            if (get_system_ms() - rs485_timeout_start > RS485_TIMEOUT_MS) {
                rs485_stats.timeout_error++;
                rs485_retry_count++;
                
                if (rs485_retry_count >= RS485_RETRY_COUNT) {
                    rs485_app_state = RS485_APP_STATE_ERROR;
                    if (error_callback != NULL) {
                        error_callback(0x01); // 超时错误
                    }
                } else {
                    rs485_app_state = RS485_APP_STATE_IDLE;
                }
            }
            break;
            
        case RS485_APP_STATE_ERROR:
            /* 错误状态处理 */
            rs485_app_state = RS485_APP_STATE_IDLE;
            rs485_retry_count = 0;
            break;
            
        default:
            rs485_app_state = RS485_APP_STATE_IDLE;
            break;
    }
}

/*!
    \brief      发送RS485帧
    \param[in]  address: 目标地址
    \param[in]  command: 命令字
    \param[in]  data: 数据指针
    \param[in]  length: 数据长度
    \param[out] none
    \retval     1: 成功, 0: 失败
*/
uint8_t rs485_send_frame(uint8_t address, uint8_t command, uint8_t *data, uint8_t length)
{
    rs485_frame_t frame;
    uint16_t frame_length;
    
    if (rs485_app_state != RS485_APP_STATE_IDLE) {
        return 0; // 忙碌状态
    }
    
    if (length > RS485_MAX_FRAME_SIZE - RS485_MIN_FRAME_SIZE) {
        return 0; // 数据长度超限
    }
    
    /* 构建帧 */
    frame.header = RS485_FRAME_HEADER;
    frame.address = address;
    frame.command = command;
    frame.length = length;
    
    if (data != NULL && length > 0) {
        memcpy(frame.data, data, length);
    }
    
    frame.tail = RS485_FRAME_TAIL;
    
    /* 构建发送缓冲区 */
    if (!rs485_build_frame(&frame, rs485_app_tx_buffer, &frame_length)) {
        return 0;
    }

    /* 发送数据 */
    if (bsp_rs485_send_data(rs485_app_tx_buffer, frame_length)) {
        rs485_app_state = RS485_APP_STATE_SENDING;
        rs485_timeout_start = get_system_ms();
        return 1;
    }
    
    return 0;
}

/*!
    \brief      发送数据
    \param[in]  address: 目标地址
    \param[in]  data: 数据指针
    \param[in]  length: 数据长度
    \param[out] none
    \retval     1: 成功, 0: 失败
*/
uint8_t rs485_send_data(uint8_t address, uint8_t *data, uint8_t length)
{
    return rs485_send_frame(address, RS485_CMD_WRITE_DATA, data, length);
}

/*!
    \brief      发送心跳包
    \param[in]  address: 目标地址
    \param[out] none
    \retval     1: 成功, 0: 失败
*/
uint8_t rs485_send_ping(uint8_t address)
{
    return rs485_send_frame(address, RS485_CMD_PING, NULL, 0);
}

/*!
    \brief      发送ACK应答
    \param[in]  address: 目标地址
    \param[in]  original_command: 原始命令
    \param[out] none
    \retval     1: 成功, 0: 失败
*/
uint8_t rs485_send_ack(uint8_t address, uint8_t original_command)
{
    uint8_t ack_data = original_command;
    return rs485_send_frame(address, RS485_CMD_ACK, &ack_data, 1);
}

/*!
    \brief      发送NACK应答
    \param[in]  address: 目标地址
    \param[in]  original_command: 原始命令
    \param[in]  error_code: 错误代码
    \param[out] none
    \retval     1: 成功, 0: 失败
*/
uint8_t rs485_send_nack(uint8_t address, uint8_t original_command, uint8_t error_code)
{
    uint8_t nack_data[2] = {original_command, error_code};
    return rs485_send_frame(address, RS485_CMD_NACK, nack_data, 2);
}

/*!
    \brief      解析RS485帧
    \param[in]  buffer: 接收缓冲区
    \param[in]  length: 缓冲区长度
    \param[out] frame: 解析后的帧结构
    \retval     1: 成功, 0: 失败
*/
uint8_t rs485_parse_frame(uint8_t *buffer, uint16_t length, rs485_frame_t *frame)
{
    if (buffer == NULL || frame == NULL || length < RS485_MIN_FRAME_SIZE) {
        return 0;
    }

    /* 查找帧头 */
    uint16_t header_pos = 0;
    for (header_pos = 0; header_pos < length; header_pos++) {
        if (buffer[header_pos] == RS485_FRAME_HEADER) {
            break;
        }
    }

    if (header_pos >= length) {
        return 0; // 未找到帧头
    }

    /* 检查剩余长度是否足够 */
    uint16_t remaining = length - header_pos;
    if (remaining < RS485_MIN_FRAME_SIZE) {
        return 0;
    }

    /* 解析帧结构 */
    frame->header = buffer[header_pos];
    frame->address = buffer[header_pos + 1];
    frame->command = buffer[header_pos + 2];
    frame->length = buffer[header_pos + 3];

    /* 检查数据长度是否合理 */
    if (frame->length > RS485_MAX_FRAME_SIZE - RS485_MIN_FRAME_SIZE) {
        return 0;
    }

    /* 检查完整帧长度 */
    uint16_t total_frame_length = RS485_MIN_FRAME_SIZE + frame->length;
    if (remaining < total_frame_length) {
        return 0;
    }

    /* 复制数据 */
    if (frame->length > 0) {
        memcpy(frame->data, &buffer[header_pos + 4], frame->length);
    }

    /* 获取校验和和帧尾 */
    frame->checksum = buffer[header_pos + 4 + frame->length];
    frame->tail = buffer[header_pos + 5 + frame->length];

    /* 检查帧尾 */
    if (frame->tail != RS485_FRAME_TAIL) {
        return 0;
    }

    return 1;
}

/*!
    \brief      构建RS485帧
    \param[in]  frame: 帧结构
    \param[out] buffer: 输出缓冲区
    \param[out] length: 输出长度
    \retval     1: 成功, 0: 失败
*/
uint8_t rs485_build_frame(rs485_frame_t *frame, uint8_t *buffer, uint16_t *length)
{
    if (frame == NULL || buffer == NULL || length == NULL) {
        return 0;
    }

    if (frame->length > RS485_MAX_FRAME_SIZE - RS485_MIN_FRAME_SIZE) {
        return 0;
    }

    uint16_t pos = 0;

    /* 构建帧 */
    buffer[pos++] = frame->header;
    buffer[pos++] = frame->address;
    buffer[pos++] = frame->command;
    buffer[pos++] = frame->length;

    /* 复制数据 */
    if (frame->length > 0) {
        memcpy(&buffer[pos], frame->data, frame->length);
        pos += frame->length;
    }

    /* 计算校验和 */
    frame->checksum = rs485_calculate_checksum(buffer, pos);
    buffer[pos++] = frame->checksum;
    buffer[pos++] = frame->tail;

    *length = pos;
    return 1;
}

/*!
    \brief      计算校验和
    \param[in]  data: 数据指针
    \param[in]  length: 数据长度
    \param[out] none
    \retval     校验和
*/
uint8_t rs485_calculate_checksum(uint8_t *data, uint16_t length)
{
    uint8_t checksum = 0;
    for (uint16_t i = 0; i < length; i++) {
        checksum += data[i];
    }
    return (~checksum + 1); // 取反加1
}

/*!
    \brief      验证帧完整性
    \param[in]  frame: 帧结构
    \param[out] none
    \retval     1: 验证通过, 0: 验证失败
*/
uint8_t rs485_verify_frame(rs485_frame_t *frame)
{
    if (frame == NULL) {
        return 0;
    }

    /* 构建临时缓冲区计算校验和 */
    uint8_t temp_buffer[RS485_MAX_FRAME_SIZE];
    uint16_t pos = 0;

    temp_buffer[pos++] = frame->header;
    temp_buffer[pos++] = frame->address;
    temp_buffer[pos++] = frame->command;
    temp_buffer[pos++] = frame->length;

    if (frame->length > 0) {
        memcpy(&temp_buffer[pos], frame->data, frame->length);
        pos += frame->length;
    }

    uint8_t calculated_checksum = rs485_calculate_checksum(temp_buffer, pos);

    return (calculated_checksum == frame->checksum);
}

/*!
    \brief      处理接收到的帧
    \param[in]  frame: 接收到的帧
    \param[out] none
    \retval     none
*/
void rs485_process_received_frame(rs485_frame_t *frame)
{
    if (frame == NULL) {
        return;
    }

    switch (frame->command) {
        case RS485_CMD_PING:
            /* 响应心跳包 */
            rs485_send_ack(frame->address, RS485_CMD_PING);
            my_printf(RS485_USART, "RS485: Ping received from 0x%02X\r\n", frame->address);
            break;

        case RS485_CMD_READ_DATA:
            /* 处理读取数据请求 */
            my_printf(RS485_USART, "RS485: Read data request from 0x%02X\r\n", frame->address);
            // 这里可以添加具体的数据读取逻辑
            rs485_send_ack(frame->address, RS485_CMD_READ_DATA);
            break;

        case RS485_CMD_WRITE_DATA:
            /* 处理写入数据请求 */
            my_printf(RS485_USART, "RS485: Write data from 0x%02X, length=%d\r\n",
                     frame->address, frame->length);
            // 这里可以添加具体的数据处理逻辑
            rs485_send_ack(frame->address, RS485_CMD_WRITE_DATA);
            break;

        case RS485_CMD_GET_STATUS:
            /* 发送状态信息 */
            my_printf(RS485_USART, "RS485: Status request from 0x%02X\r\n", frame->address);
            // 这里可以发送设备状态
            rs485_send_ack(frame->address, RS485_CMD_GET_STATUS);
            break;

        case RS485_CMD_ACK:
            my_printf(RS485_USART, "RS485: ACK received from 0x%02X\r\n", frame->address);
            break;

        case RS485_CMD_NACK:
            my_printf(RS485_USART, "RS485: NACK received from 0x%02X\r\n", frame->address);
            break;

        default:
            my_printf(RS485_USART, "RS485: Unknown command 0x%02X from 0x%02X\r\n",
                     frame->command, frame->address);
            rs485_send_nack(frame->address, frame->command, 0x01); // 未知命令
            break;
    }
}

/*!
    \brief      设置本地地址
    \param[in]  address: 本地地址
    \param[out] none
    \retval     none
*/
void rs485_set_local_address(uint8_t address)
{
    rs485_local_address = address;
}

/*!
    \brief      获取本地地址
    \param[in]  none
    \param[out] none
    \retval     本地地址
*/
uint8_t rs485_get_local_address(void)
{
    return rs485_local_address;
}

/*!
    \brief      设置帧接收回调函数
    \param[in]  callback: 回调函数指针
    \param[out] none
    \retval     none
*/
void rs485_set_frame_received_callback(rs485_frame_received_callback_t callback)
{
    frame_received_callback = callback;
}

/*!
    \brief      设置错误回调函数
    \param[in]  callback: 回调函数指针
    \param[out] none
    \retval     none
*/
void rs485_set_error_callback(rs485_error_callback_t callback)
{
    error_callback = callback;
}

/*!
    \brief      获取应用状态
    \param[in]  none
    \param[out] none
    \retval     应用状态
*/
rs485_app_state_t rs485_get_app_state(void)
{
    return rs485_app_state;
}

/*!
    \brief      获取统计信息
    \param[in]  none
    \param[out] none
    \retval     统计信息指针
*/
rs485_statistics_t* rs485_get_statistics(void)
{
    return &rs485_stats;
}

/*!
    \brief      清除统计信息
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rs485_clear_statistics(void)
{
    memset(&rs485_stats, 0, sizeof(rs485_statistics_t));
}

/*!
    \brief      打印统计信息
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rs485_print_statistics(void)
{
    my_printf(RS485_USART, "=== RS485 Statistics ===\r\n");
    my_printf(RS485_USART, "Frames sent: %lu\r\n", rs485_stats.frames_sent);
    my_printf(RS485_USART, "Frames received: %lu\r\n", rs485_stats.frames_received);
    my_printf(RS485_USART, "Frames error: %lu\r\n", rs485_stats.frames_error);
    my_printf(RS485_USART, "Checksum error: %lu\r\n", rs485_stats.checksum_error);
    my_printf(RS485_USART, "Timeout error: %lu\r\n", rs485_stats.timeout_error);
    my_printf(RS485_USART, "Last communication: %lu ms\r\n", rs485_stats.last_communication_time);
    my_printf(RS485_USART, "=======================\r\n");
}

/*!
    \brief      RS485自检测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rs485_self_test(void)
{
    my_printf(RS485_USART, "=== RS485 Self Test ===\r\n");

    /* 测试底层驱动状态 */
    rs485_state_t bsp_state = bsp_rs485_get_state();
    my_printf(RS485_USART, "BSP State: %d\r\n", bsp_state);

    /* 测试应用层状态 */
    rs485_app_state_t app_state = rs485_get_app_state();
    my_printf(RS485_USART, "App State: %d\r\n", app_state);

    /* 测试本地地址 */
    uint8_t local_addr = rs485_get_local_address();
    my_printf(RS485_USART, "Local Address: 0x%02X\r\n", local_addr);

    /* 测试帧构建和解析 */
    rs485_frame_t test_frame;
    uint8_t test_buffer[64];
    uint16_t buffer_length;

    test_frame.header = RS485_FRAME_HEADER;
    test_frame.address = 0x02;
    test_frame.command = RS485_CMD_PING;
    test_frame.length = 0;
    test_frame.tail = RS485_FRAME_TAIL;

    if (rs485_build_frame(&test_frame, test_buffer, &buffer_length)) {
        my_printf(RS485_USART, "Frame build: OK, length=%d\r\n", buffer_length);

        /* 测试帧解析 */
        rs485_frame_t parsed_frame;
        if (rs485_parse_frame(test_buffer, buffer_length, &parsed_frame)) {
            my_printf(RS485_USART, "Frame parse: OK\r\n");

            /* 测试帧验证 */
            if (rs485_verify_frame(&parsed_frame)) {
                my_printf(RS485_USART, "Frame verify: OK\r\n");
            } else {
                my_printf(RS485_USART, "Frame verify: FAILED\r\n");
            }
        } else {
            my_printf(RS485_USART, "Frame parse: FAILED\r\n");
        }
    } else {
        my_printf(RS485_USART, "Frame build: FAILED\r\n");
    }

    my_printf(RS485_USART, "======================\r\n");
}

/*!
    \brief      RS485回环测试
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rs485_loopback_test(void)
{
    my_printf(RS485_USART, "=== RS485 Loopback Test ===\r\n");

    /* 发送测试数据 */
    uint8_t test_data[] = "Hello RS485!";
    uint8_t test_length = strlen((char*)test_data);

    if (rs485_send_data(rs485_local_address, test_data, test_length)) {
        my_printf(RS485_USART, "Test data sent: %s\r\n", test_data);
    } else {
        my_printf(RS485_USART, "Test data send failed\r\n");
    }

    my_printf(RS485_USART, "===========================\r\n");
}

/*!
    \brief      RS485通信测试
    \param[in]  target_address: 目标地址
    \param[out] none
    \retval     none
*/
void rs485_communication_test(uint8_t target_address)
{
    my_printf(RS485_USART, "=== RS485 Communication Test ===\r\n");
    my_printf(RS485_USART, "Target Address: 0x%02X\r\n", target_address);

    /* 发送心跳包 */
    if (rs485_send_ping(target_address)) {
        my_printf(RS485_USART, "Ping sent to 0x%02X\r\n", target_address);
    } else {
        my_printf(RS485_USART, "Ping send failed\r\n");
    }

    /* 发送测试数据 */
    uint8_t test_data[] = {0x01, 0x02, 0x03, 0x04, 0x05};
    if (rs485_send_data(target_address, test_data, sizeof(test_data))) {
        my_printf(RS485_USART, "Test data sent to 0x%02X\r\n", target_address);
    } else {
        my_printf(RS485_USART, "Test data send failed\r\n");
    }

    my_printf(RS485_USART, "================================\r\n");
}

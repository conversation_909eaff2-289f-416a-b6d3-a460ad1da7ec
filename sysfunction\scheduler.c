#include "cmic_gd32f470vet6.h"

// 存储任务数量的全局变量
uint8_t task_num;

typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t rate_ms;           // 任务执行周期（毫秒）
    uint32_t last_run;          // 上次执行时间（毫秒）
} task_t;

// 静态任务数组，每个元素包含：函数指针、执行周期（毫秒）、上次运行时间（毫秒）
static task_t scheduler_task[] =
{
     {led_task,     1,    0}    // LED任务，1毫秒周期
    ,{oled_task,    500,  0}    // OLED任务，500毫秒周期
    ,{btn_task,     5,    0}    // 按键任务，5毫秒周期
    ,{uart_task,    5,    0}    // UART任务，5毫秒周期
    ,{rs485_task,   10,   0}    // RS485任务，10毫秒周期
    ,{rtc_task,     500,  0}    // RTC任务，500毫秒周期
    ,{sampling_task, 100, 0}    // 采样任务，100毫秒周期（需要时包含ADC）
    ,{log_periodic_sync, 100, 0}  // 日志同步任务，100毫秒周期（每1秒同步一次）
};

/**
 * @brief 任务调度器初始化函数
 * 计算任务数组元素数量并存储在task_num中
 */
void scheduler_init(void)
{
    // 计算任务数组元素数量并存储在task_num中
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

/**
 * @brief 任务调度器运行函数
 * 遍历任务数组，检查每个任务是否需要执行。
 * 如果当前时间已达到任务执行周期，则执行任务并更新上次运行时间
 */
void scheduler_run(void)
{
    // 遍历任务数组中的所有任务
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 获取当前系统时间（毫秒）
        uint32_t now_time = get_system_ms();

        // 检查当前时间是否已达到任务执行时间
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            // 将任务上次运行时间更新为当前时间
            scheduler_task[i].last_run = now_time;

            // 执行任务
            scheduler_task[i].task_func();
        }
    }
}



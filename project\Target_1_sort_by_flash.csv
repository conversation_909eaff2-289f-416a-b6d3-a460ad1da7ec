File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,17.196264%,12815,96,12264,551,0,96
usart_app.o,10.533802%,7850,1576,7434,384,32,1544
ff.o,9.826629%,7323,520,7174,141,8,512
sdio_sdcard.o,8.491452%,6328,64,6296,0,32,32
rs485_app.o,5.324602%,3968,1196,3948,0,20,1176
oled.o,5.193097%,3870,22,1136,2712,22,0
btod.o,2.887738%,2152,0,2152,0,0,0
cmic_gd32f470vet6.o,2.879687%,2146,1626,2124,0,22,1604
fz_wm.l,2.042350%,1522,0,1506,16,0,0
sd_app.o,1.908161%,1422,2516,1280,110,32,2484
log_app.o,1.867905%,1392,660,1380,0,12,648
scanf_fp.o,1.706879%,1272,0,1272,0,0,0
_printf_fp_dec.o,1.414347%,1054,0,1054,0,0,0
gd32f4xx_dma.o,1.400929%,1044,0,1044,0,0,0
gd25qxx.o,1.196962%,892,0,892,0,0,0
_scanf.o,1.186227%,884,0,884,0,0,0
m_wm.l,1.076192%,802,0,802,0,0,0
_printf_fp_hex.o,1.076192%,802,0,764,38,0,0
scanf_hexfp.o,1.073509%,800,0,800,0,0,0
gd32f4xx_rcu.o,0.992995%,740,0,740,0,0,0
gd32f4xx_usart.o,0.915166%,682,0,682,0,0,0
perf_counter.o,0.882961%,658,64,590,4,64,0
gd32f4xx_adc.o,0.738037%,550,0,550,0,0,0
gd32f4xx_sdio.o,0.732670%,546,0,546,0,0,0
system_gd32f4xx.o,0.695097%,518,4,514,0,4,0
startup_gd32f450_470.o,0.660208%,492,8192,64,428,0,8192
gd32f4xx_i2c.o,0.587746%,438,0,438,0,0,0
__printf_flags_ss_wp.o,0.548831%,409,0,392,17,0,0
gd32f4xx_rtc.o,0.547489%,408,0,408,0,0,0
oled_app.o,0.542122%,404,16,388,0,16,0
bigflt0.o,0.504549%,376,0,228,148,0,0
diskio.o,0.483079%,360,0,360,0,0,0
dmul.o,0.456241%,340,0,340,0,0,0
rtc_app.o,0.456241%,340,0,340,0,0,0
main.o,0.456241%,340,0,340,0,0,0
_scanf_int.o,0.445506%,332,0,332,0,0,0
lc_ctype_c.o,0.424036%,316,0,44,272,0,0
scanf_infnan.o,0.413301%,308,0,308,0,0,0
narrow.o,0.356942%,266,0,266,0,0,0
gd32f4xx_gpio.o,0.354258%,264,0,264,0,0,0
lludivv7m.o,0.319369%,238,0,238,0,0,0
ldexp.o,0.305950%,228,0,228,0,0,0
_printf_wctomb.o,0.263010%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.252274%,188,0,148,40,0,0
btn_app.o,0.252274%,188,4,184,0,4,0
_printf_intcommon.o,0.238856%,178,0,178,0,0,0
gd32f4xx_misc.o,0.236172%,176,0,176,0,0,0
scheduler.o,0.230804%,172,100,72,0,100,0
strtod.o,0.220069%,164,0,164,0,0,0
led_app.o,0.218727%,163,7,156,0,7,0
dnaninf.o,0.209334%,156,0,156,0,0,0
strncmp.o,0.201283%,150,0,150,0,0,0
systick.o,0.198599%,148,4,144,0,4,0
gd32f4xx_it.o,0.198599%,148,0,148,0,0,0
frexp.o,0.187864%,140,0,140,0,0,0
fnaninf.o,0.187864%,140,0,140,0,0,0
rt_memcpy_v6.o,0.185180%,138,0,138,0,0,0
lludiv10.o,0.185180%,138,0,138,0,0,0
strcmpv7m.o,0.171761%,128,0,128,0,0,0
_printf_fp_infnan.o,0.171761%,128,0,128,0,0,0
_printf_longlong_dec.o,0.166394%,124,0,124,0,0,0
perfc_port_default.o,0.163710%,122,0,122,0,0,0
dleqf.o,0.161026%,120,0,120,0,0,0
deqf.o,0.161026%,120,0,120,0,0,0
_printf_dec.o,0.161026%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.150291%,112,0,112,0,0,0
drleqf.o,0.144924%,108,0,108,0,0,0
gd32f4xx_spi.o,0.139556%,104,0,104,0,0,0
retnan.o,0.134189%,100,0,100,0,0,0
rt_memcpy_w.o,0.134189%,100,0,100,0,0,0
d2f.o,0.131505%,98,0,98,0,0,0
scalbn.o,0.123453%,92,0,92,0,0,0
__dczerorl2.o,0.120770%,90,0,90,0,0,0
f2d.o,0.115402%,86,0,86,0,0,0
strncpy.o,0.115402%,86,0,86,0,0,0
_printf_str.o,0.110035%,82,0,82,0,0,0
rt_memclr_w.o,0.104667%,78,0,78,0,0,0
_printf_pad.o,0.104667%,78,0,78,0,0,0
sys_stackheap_outer.o,0.099300%,74,0,74,0,0,0
strcpy.o,0.096616%,72,0,72,0,0,0
llsdiv.o,0.096616%,72,0,72,0,0,0
lc_numeric_c.o,0.096616%,72,0,44,28,0,0
rt_memclr.o,0.091248%,68,0,68,0,0,0
dunder.o,0.085881%,64,0,64,0,0,0
_wcrtomb.o,0.085881%,64,0,64,0,0,0
_sgetc.o,0.085881%,64,0,64,0,0,0
strlen.o,0.083197%,62,0,62,0,0,0
__0sscanf.o,0.080513%,60,0,60,0,0,0
atof.o,0.075146%,56,0,56,0,0,0
__2snprintf.o,0.075146%,56,0,56,0,0,0
vsnprintf.o,0.069778%,52,0,52,0,0,0
__scatter.o,0.069778%,52,0,52,0,0,0
fpclassify.o,0.064411%,48,0,48,0,0,0
trapv.o,0.064411%,48,0,48,0,0,0
_printf_char_common.o,0.064411%,48,0,48,0,0,0
scanf_char.o,0.059043%,44,0,44,0,0,0
_printf_wchar.o,0.059043%,44,0,44,0,0,0
_printf_char.o,0.059043%,44,0,44,0,0,0
_printf_charcount.o,0.053675%,40,0,40,0,0,0
llshl.o,0.050992%,38,0,38,0,0,0
libinit2.o,0.050992%,38,0,38,0,0,0
strstr.o,0.048308%,36,0,36,0,0,0
init_aeabi.o,0.048308%,36,0,36,0,0,0
_printf_truncate.o,0.048308%,36,0,36,0,0,0
systick_wrapper_ual.o,0.042940%,32,0,32,0,0,0
_chval.o,0.037573%,28,0,28,0,0,0
__scatter_zi.o,0.037573%,28,0,28,0,0,0
dcmpi.o,0.032205%,24,0,24,0,0,0
_rserrno.o,0.029521%,22,0,22,0,0,0
strchr.o,0.026838%,20,0,20,0,0,0
isspace.o,0.024154%,18,0,18,0,0,0
exit.o,0.024154%,18,0,18,0,0,0
fpconst.o,0.021470%,16,0,0,16,0,0
dcheck1.o,0.021470%,16,0,16,0,0,0
rt_ctype_table.o,0.021470%,16,0,16,0,0,0
_snputc.o,0.021470%,16,0,16,0,0,0
gd32f4xx_pmu.o,0.021470%,16,0,16,0,0,0
__printf_wp.o,0.018786%,14,0,14,0,0,0
dretinf.o,0.016103%,12,0,12,0,0,0
sys_exit.o,0.016103%,12,0,12,0,0,0
__rtentry2.o,0.016103%,12,0,12,0,0,0
fretinf.o,0.013419%,10,0,10,0,0,0
fpinit.o,0.013419%,10,0,10,0,0,0
rtexit2.o,0.013419%,10,0,10,0,0,0
_sputc.o,0.013419%,10,0,10,0,0,0
_printf_ll.o,0.013419%,10,0,10,0,0,0
_printf_l.o,0.013419%,10,0,10,0,0,0
scanf2.o,0.010735%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.010735%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.010735%,8,0,8,0,0,0
libspace.o,0.010735%,8,96,8,0,0,96
__main.o,0.010735%,8,0,8,0,0,0
istatus.o,0.008051%,6,0,6,0,0,0
heapauxi.o,0.008051%,6,0,6,0,0,0
_printf_x.o,0.008051%,6,0,6,0,0,0
_printf_u.o,0.008051%,6,0,6,0,0,0
_printf_s.o,0.008051%,6,0,6,0,0,0
_printf_p.o,0.008051%,6,0,6,0,0,0
_printf_o.o,0.008051%,6,0,6,0,0,0
_printf_n.o,0.008051%,6,0,6,0,0,0
_printf_ls.o,0.008051%,6,0,6,0,0,0
_printf_llx.o,0.008051%,6,0,6,0,0,0
_printf_llu.o,0.008051%,6,0,6,0,0,0
_printf_llo.o,0.008051%,6,0,6,0,0,0
_printf_lli.o,0.008051%,6,0,6,0,0,0
_printf_lld.o,0.008051%,6,0,6,0,0,0
_printf_lc.o,0.008051%,6,0,6,0,0,0
_printf_i.o,0.008051%,6,0,6,0,0,0
_printf_g.o,0.008051%,6,0,6,0,0,0
_printf_f.o,0.008051%,6,0,6,0,0,0
_printf_e.o,0.008051%,6,0,6,0,0,0
_printf_d.o,0.008051%,6,0,6,0,0,0
_printf_c.o,0.008051%,6,0,6,0,0,0
_printf_a.o,0.008051%,6,0,6,0,0,0
__rtentry4.o,0.008051%,6,0,6,0,0,0
scanf1.o,0.005368%,4,0,4,0,0,0
printf2.o,0.005368%,4,0,4,0,0,0
printf1.o,0.005368%,4,0,4,0,0,0
_printf_percent_end.o,0.005368%,4,0,4,0,0,0
use_no_semi.o,0.002684%,2,0,2,0,0,0
rtexit.o,0.002684%,2,0,2,0,0,0
libshutdown2.o,0.002684%,2,0,2,0,0,0
libshutdown.o,0.002684%,2,0,2,0,0,0
libinit.o,0.002684%,2,0,2,0,0,0

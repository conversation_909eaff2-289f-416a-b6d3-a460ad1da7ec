File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,17.139227%,12815,96,12264,551,0,96
usart_app.o,10.498863%,7850,1576,7434,384,32,1544
ff.o,9.794035%,7323,520,7174,141,8,512
sdio_sdcard.o,8.463287%,6328,64,6296,0,32,32
rs485_app.o,5.306942%,3968,1196,3948,0,20,1176
oled.o,5.175873%,3870,22,1136,2712,22,0
cmic_gd32f470vet6.o,3.201819%,2394,1626,2372,0,22,1604
btod.o,2.878160%,2152,0,2152,0,0,0
fz_wm.l,2.035576%,1522,0,1506,16,0,0
sd_app.o,1.901832%,1422,2516,1280,110,32,2484
log_app.o,1.861709%,1392,660,1380,0,12,648
scanf_fp.o,1.701217%,1272,0,1272,0,0,0
_printf_fp_dec.o,1.409656%,1054,0,1054,0,0,0
gd32f4xx_dma.o,1.396282%,1044,0,1044,0,0,0
gd25qxx.o,1.192992%,892,0,892,0,0,0
_scanf.o,1.182292%,884,0,884,0,0,0
m_wm.l,1.072623%,802,0,802,0,0,0
_printf_fp_hex.o,1.072623%,802,0,764,38,0,0
scanf_hexfp.o,1.069948%,800,0,800,0,0,0
gd32f4xx_rcu.o,0.989702%,740,0,740,0,0,0
gd32f4xx_usart.o,0.912131%,682,0,682,0,0,0
perf_counter.o,0.880032%,658,64,590,4,64,0
gd32f4xx_adc.o,0.735589%,550,0,550,0,0,0
gd32f4xx_sdio.o,0.730239%,546,0,546,0,0,0
system_gd32f4xx.o,0.692791%,518,4,514,0,4,0
startup_gd32f450_470.o,0.658018%,492,8192,64,428,0,8192
gd32f4xx_i2c.o,0.585796%,438,0,438,0,0,0
__printf_flags_ss_wp.o,0.547011%,409,0,392,17,0,0
gd32f4xx_rtc.o,0.545673%,408,0,408,0,0,0
oled_app.o,0.540324%,404,16,388,0,16,0
bigflt0.o,0.502876%,376,0,228,148,0,0
diskio.o,0.481477%,360,0,360,0,0,0
dmul.o,0.454728%,340,0,340,0,0,0
rtc_app.o,0.454728%,340,0,340,0,0,0
main.o,0.454728%,340,0,340,0,0,0
_scanf_int.o,0.444028%,332,0,332,0,0,0
lc_ctype_c.o,0.422629%,316,0,44,272,0,0
scanf_infnan.o,0.411930%,308,0,308,0,0,0
narrow.o,0.355758%,266,0,266,0,0,0
gd32f4xx_gpio.o,0.353083%,264,0,264,0,0,0
lludivv7m.o,0.318309%,238,0,238,0,0,0
ldexp.o,0.304935%,228,0,228,0,0,0
_printf_wctomb.o,0.262137%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.251438%,188,0,148,40,0,0
btn_app.o,0.251438%,188,4,184,0,4,0
_printf_intcommon.o,0.238063%,178,0,178,0,0,0
gd32f4xx_misc.o,0.235389%,176,0,176,0,0,0
scheduler.o,0.230039%,172,100,72,0,100,0
strtod.o,0.219339%,164,0,164,0,0,0
led_app.o,0.218002%,163,7,156,0,7,0
dnaninf.o,0.208640%,156,0,156,0,0,0
strncmp.o,0.200615%,150,0,150,0,0,0
systick.o,0.197940%,148,4,144,0,4,0
gd32f4xx_it.o,0.197940%,148,0,148,0,0,0
frexp.o,0.187241%,140,0,140,0,0,0
fnaninf.o,0.187241%,140,0,140,0,0,0
rt_memcpy_v6.o,0.184566%,138,0,138,0,0,0
lludiv10.o,0.184566%,138,0,138,0,0,0
strcmpv7m.o,0.171192%,128,0,128,0,0,0
_printf_fp_infnan.o,0.171192%,128,0,128,0,0,0
_printf_longlong_dec.o,0.165842%,124,0,124,0,0,0
perfc_port_default.o,0.163167%,122,0,122,0,0,0
dleqf.o,0.160492%,120,0,120,0,0,0
deqf.o,0.160492%,120,0,120,0,0,0
_printf_dec.o,0.160492%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.149793%,112,0,112,0,0,0
drleqf.o,0.144443%,108,0,108,0,0,0
gd32f4xx_spi.o,0.139093%,104,0,104,0,0,0
retnan.o,0.133743%,100,0,100,0,0,0
rt_memcpy_w.o,0.133743%,100,0,100,0,0,0
d2f.o,0.131069%,98,0,98,0,0,0
scalbn.o,0.123044%,92,0,92,0,0,0
__dczerorl2.o,0.120369%,90,0,90,0,0,0
f2d.o,0.115019%,86,0,86,0,0,0
strncpy.o,0.115019%,86,0,86,0,0,0
_printf_str.o,0.109670%,82,0,82,0,0,0
rt_memclr_w.o,0.104320%,78,0,78,0,0,0
_printf_pad.o,0.104320%,78,0,78,0,0,0
sys_stackheap_outer.o,0.098970%,74,0,74,0,0,0
strcpy.o,0.096295%,72,0,72,0,0,0
llsdiv.o,0.096295%,72,0,72,0,0,0
lc_numeric_c.o,0.096295%,72,0,44,28,0,0
rt_memclr.o,0.090946%,68,0,68,0,0,0
dunder.o,0.085596%,64,0,64,0,0,0
_wcrtomb.o,0.085596%,64,0,64,0,0,0
_sgetc.o,0.085596%,64,0,64,0,0,0
strlen.o,0.082921%,62,0,62,0,0,0
__0sscanf.o,0.080246%,60,0,60,0,0,0
atof.o,0.074896%,56,0,56,0,0,0
__2snprintf.o,0.074896%,56,0,56,0,0,0
vsnprintf.o,0.069547%,52,0,52,0,0,0
__scatter.o,0.069547%,52,0,52,0,0,0
fpclassify.o,0.064197%,48,0,48,0,0,0
trapv.o,0.064197%,48,0,48,0,0,0
_printf_char_common.o,0.064197%,48,0,48,0,0,0
scanf_char.o,0.058847%,44,0,44,0,0,0
_printf_wchar.o,0.058847%,44,0,44,0,0,0
_printf_char.o,0.058847%,44,0,44,0,0,0
_printf_charcount.o,0.053497%,40,0,40,0,0,0
llshl.o,0.050823%,38,0,38,0,0,0
libinit2.o,0.050823%,38,0,38,0,0,0
strstr.o,0.048148%,36,0,36,0,0,0
init_aeabi.o,0.048148%,36,0,36,0,0,0
_printf_truncate.o,0.048148%,36,0,36,0,0,0
systick_wrapper_ual.o,0.042798%,32,0,32,0,0,0
_chval.o,0.037448%,28,0,28,0,0,0
__scatter_zi.o,0.037448%,28,0,28,0,0,0
dcmpi.o,0.032098%,24,0,24,0,0,0
_rserrno.o,0.029424%,22,0,22,0,0,0
strchr.o,0.026749%,20,0,20,0,0,0
isspace.o,0.024074%,18,0,18,0,0,0
exit.o,0.024074%,18,0,18,0,0,0
fpconst.o,0.021399%,16,0,0,16,0,0
dcheck1.o,0.021399%,16,0,16,0,0,0
rt_ctype_table.o,0.021399%,16,0,16,0,0,0
_snputc.o,0.021399%,16,0,16,0,0,0
gd32f4xx_pmu.o,0.021399%,16,0,16,0,0,0
__printf_wp.o,0.018724%,14,0,14,0,0,0
dretinf.o,0.016049%,12,0,12,0,0,0
sys_exit.o,0.016049%,12,0,12,0,0,0
__rtentry2.o,0.016049%,12,0,12,0,0,0
fretinf.o,0.013374%,10,0,10,0,0,0
fpinit.o,0.013374%,10,0,10,0,0,0
rtexit2.o,0.013374%,10,0,10,0,0,0
_sputc.o,0.013374%,10,0,10,0,0,0
_printf_ll.o,0.013374%,10,0,10,0,0,0
_printf_l.o,0.013374%,10,0,10,0,0,0
scanf2.o,0.010699%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.010699%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.010699%,8,0,8,0,0,0
libspace.o,0.010699%,8,96,8,0,0,96
__main.o,0.010699%,8,0,8,0,0,0
istatus.o,0.008025%,6,0,6,0,0,0
heapauxi.o,0.008025%,6,0,6,0,0,0
_printf_x.o,0.008025%,6,0,6,0,0,0
_printf_u.o,0.008025%,6,0,6,0,0,0
_printf_s.o,0.008025%,6,0,6,0,0,0
_printf_p.o,0.008025%,6,0,6,0,0,0
_printf_o.o,0.008025%,6,0,6,0,0,0
_printf_n.o,0.008025%,6,0,6,0,0,0
_printf_ls.o,0.008025%,6,0,6,0,0,0
_printf_llx.o,0.008025%,6,0,6,0,0,0
_printf_llu.o,0.008025%,6,0,6,0,0,0
_printf_llo.o,0.008025%,6,0,6,0,0,0
_printf_lli.o,0.008025%,6,0,6,0,0,0
_printf_lld.o,0.008025%,6,0,6,0,0,0
_printf_lc.o,0.008025%,6,0,6,0,0,0
_printf_i.o,0.008025%,6,0,6,0,0,0
_printf_g.o,0.008025%,6,0,6,0,0,0
_printf_f.o,0.008025%,6,0,6,0,0,0
_printf_e.o,0.008025%,6,0,6,0,0,0
_printf_d.o,0.008025%,6,0,6,0,0,0
_printf_c.o,0.008025%,6,0,6,0,0,0
_printf_a.o,0.008025%,6,0,6,0,0,0
__rtentry4.o,0.008025%,6,0,6,0,0,0
scanf1.o,0.005350%,4,0,4,0,0,0
printf2.o,0.005350%,4,0,4,0,0,0
printf1.o,0.005350%,4,0,4,0,0,0
_printf_percent_end.o,0.005350%,4,0,4,0,0,0
use_no_semi.o,0.002675%,2,0,2,0,0,0
rtexit.o,0.002675%,2,0,2,0,0,0
libshutdown2.o,0.002675%,2,0,2,0,0,0
libshutdown.o,0.002675%,2,0,2,0,0,0
libinit.o,0.002675%,2,0,2,0,0,0

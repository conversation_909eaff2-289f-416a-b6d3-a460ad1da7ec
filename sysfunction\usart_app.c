#include "cmic_gd32f470vet6.h"
#include "sd_app.h"
#include "log_app.h"

static uint8_t auto_log_enabled = 1;

#define PRE_TEST_BUFFER_SIZE 1024
#define PRE_TEST_BUFFER_FLASH_ADDR 0x00050000
#define PRE_TEST_BUFFER_MAGIC 0x12345678

typedef struct {
    uint32_t magic;
    uint16_t buffer_pos;
    uint8_t logging_enabled;
    uint8_t reserved;
    char buffer_data[PRE_TEST_BUFFER_SIZE];
} pre_test_buffer_t;

static pre_test_buffer_t pre_test_buffer;
uint16_t pre_test_buffer_pos = 0;
uint8_t pre_test_logging = 1;

void load_pre_test_buffer_from_flash(void)
{
    spi_flash_buffer_read((uint8_t*)&pre_test_buffer, PRE_TEST_BUFFER_FLASH_ADDR, sizeof(pre_test_buffer_t));

    if(pre_test_buffer.magic == PRE_TEST_BUFFER_MAGIC)
    {
        pre_test_buffer_pos = pre_test_buffer.buffer_pos;
        pre_test_logging = pre_test_buffer.logging_enabled;
    }
    else
    {
        pre_test_buffer.magic = PRE_TEST_BUFFER_MAGIC;
        pre_test_buffer.buffer_pos = 0;
        pre_test_buffer.logging_enabled = 1;
        pre_test_buffer_pos = 0;
        pre_test_logging = 1;
        memset(pre_test_buffer.buffer_data, 0, PRE_TEST_BUFFER_SIZE);
    }
}

void save_pre_test_buffer_to_flash(void)
{
    pre_test_buffer.buffer_pos = pre_test_buffer_pos;
    pre_test_buffer.logging_enabled = pre_test_logging;

    spi_flash_sector_erase(PRE_TEST_BUFFER_FLASH_ADDR);
    spi_flash_buffer_write((uint8_t*)&pre_test_buffer, PRE_TEST_BUFFER_FLASH_ADDR, sizeof(pre_test_buffer_t));
}

void clear_pre_test_buffer(void)
{
    pre_test_buffer.magic = PRE_TEST_BUFFER_MAGIC;
    pre_test_buffer.buffer_pos = 0;
    pre_test_buffer.logging_enabled = 1;
    pre_test_buffer_pos = 0;
    pre_test_logging = 1;
    memset(pre_test_buffer.buffer_data, 0, PRE_TEST_BUFFER_SIZE);

    save_pre_test_buffer_to_flash();
}

__IO uint16_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[512] = {0};
int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;

    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    for(tx_count = 0; tx_count < len; tx_count++){
        usart_data_transmit(usart_periph, buffer[tx_count]);
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
    }

    if(len > 0)
    {
        char log_msg[512];
        strcpy(log_msg, buffer);
        char* newline = strstr(log_msg, "\r\n");
        if(newline) *newline = '\0';
        if(strlen(log_msg) > 0)
        {
            if(pre_test_logging)
            {
                char formatted_msg[256];
                snprintf(formatted_msg, sizeof(formatted_msg), "OUT: %s", log_msg);
                add_to_pre_test_buffer(formatted_msg);
            }
            else if(auto_log_enabled)
            {
                log_write_formatted("OUT: %s", log_msg);
            }
        }
    }

    return len;
}

uint8_t test_flash(void)
{
    uint32_t flash_id = spi_flash_read_id();

    if(flash_id != 0x000000 && flash_id != 0xFFFFFF)
    {
        my_printf(DEBUG_USART, "flash......OK\r\n");
        my_printf(DEBUG_USART, "flashID: 0x%06lX\r\n", flash_id);
        return 1;
    }
    else
    {
        my_printf(DEBUG_USART, "flash......error\r\n");
        my_printf(DEBUG_USART, "flashID: 0x%06lX\r\n", flash_id);
        return 0;
    }
}

uint8_t test_tf_card(void)
{
    if(!init_filesystem())
    {
        my_printf(DEBUG_USART, "TF card......error\r\n");
        return 0;
    }
    DIR test_dir;
    FRESULT res = f_opendir(&test_dir, "0:/");

    if(res == FR_OK)
    {
        f_closedir(&test_dir);

        DWORD fre_clust, fre_sect, tot_sect;
        FATFS* fs_ptr = NULL;

        res = f_getfree("0:", &fre_clust, &fs_ptr);

        if(res == FR_OK && fs_ptr != NULL)
        {
            tot_sect = (fs_ptr->n_fatent - 2) * fs_ptr->csize;
            fre_sect = fre_clust * fs_ptr->csize;

            my_printf(DEBUG_USART, "TF card......OK\r\n");
            my_printf(DEBUG_USART, "TF card memory: %lu KB total, %lu KB free\r\n",
                     tot_sect / 2, fre_sect / 2);
        }
        else
        {
            my_printf(DEBUG_USART, "TF card......OK\r\n");
            my_printf(DEBUG_USART, "TF card detected (size info unavailable)\r\n");
        }

        return 1;
    }

    my_printf(DEBUG_USART, "TF card......error\r\n");
    return 0;
}

typedef struct {
    uint32_t ratio;
    uint32_t limit;
    uint32_t sampling_cycle;
    uint32_t magic;
} config_data_t;

typedef struct {
    char device_id[32];
    uint32_t magic;
} device_id_t;

#define CONFIG_MAGIC 0x12345678
#define DEVICE_ID_MAGIC 0x87654321
#define CONFIG_FLASH_ADDR 0x00010000
#define DEVICE_ID_FLASH_ADDR 0x00020000

uint32_t current_ratio = 199;
uint32_t current_limit = 1011;
static uint8_t waiting_for_ratio_input = 0;
static uint8_t waiting_for_limit_input = 0;
static uint8_t waiting_for_rtc_input = 0;

static uint8_t sampling_enabled = 0;
static uint32_t sampling_counter = 0;
static uint32_t led_counter = 0;
static uint32_t sampling_period = 50;
static uint8_t hide_mode = 0;

void add_to_pre_test_buffer(const char* message)
{
    if(!pre_test_logging) return;

    char timestamped_msg[256];
    char current_time[32];

    rtc_get_time_string(current_time, sizeof(current_time));
    snprintf(timestamped_msg, sizeof(timestamped_msg), "%s %s\r\n", current_time, message);

    uint16_t msg_len = strlen(timestamped_msg);

    if(pre_test_buffer_pos + msg_len < PRE_TEST_BUFFER_SIZE)
    {
        strcpy(&pre_test_buffer.buffer_data[pre_test_buffer_pos], timestamped_msg);
        pre_test_buffer_pos += msg_len;

        save_pre_test_buffer_to_flash();
    }
}

/**
 * @brief
 */
void flush_pre_test_buffer_to_log(void)
{
    if(pre_test_buffer_pos > 0)
    {
        // 
        char* line_start = pre_test_buffer.buffer_data;
        char* line_end;

        // 
        while((line_end = strstr(line_start, "\r\n")) != NULL)
        {
            *line_end = '\0';  

            if(strlen(line_start) > 0)
            {
                log_write_raw(line_start);  // 
						}

            *line_end = '\r';  // 
            line_start = line_end + 2;  // 
        }

        clear_pre_test_buffer();
    }
}

/**
 * @brief 
 *
 * @param time_str 
 * @return uint8_t 
 */
uint8_t parse_and_set_rtc_time(char* time_str)
{
    uint16_t year;
    uint8_t month, day, hour, minute, second;

    // 
    while(*time_str == ' ') time_str++;

    int parsed = sscanf(time_str, "%hu %hhu %hhu %hhu:%hhu:%hhu",
                       &year, &month, &day, &hour, &minute, &second);

    if(parsed != 6)
    {
        return 0; // 
    }

    return rtc_set_time(year, month, day, hour, minute, second);
}

uint8_t process_ratio_input(char* input_str)
{
    float ratio_value = atof(input_str);

    if(ratio_value < 0.0f || ratio_value > 100.0f)
    {
        my_printf(DEBUG_USART, "ratio invalid\r\n");
        my_printf(DEBUG_USART, "Ratio=%lu.%02lu\r\n", current_ratio/100, current_ratio%100);

        log_write_formatted("Invalid ratio input: %s", input_str);
        return 0;
    }

    current_ratio = (uint32_t)(ratio_value * 100.0f + 0.5f);

    my_printf(DEBUG_USART, "ratio modified success\r\n");
    my_printf(DEBUG_USART, "Ratio=%lu.%02lu\r\n", current_ratio/100, current_ratio%100);

    log_write_formatted("Ratio modified to %lu.%02lu", current_ratio/100, current_ratio%100);

    return 1;
}

uint8_t process_limit_input(char* input_str)
{
    float limit_value = atof(input_str);

    if(limit_value < 0.0f || limit_value > 200.0f)
    {
        my_printf(DEBUG_USART, "limit invalid\r\n");
        my_printf(DEBUG_USART, "Limit=%lu.%02lu\r\n", current_limit/100, current_limit%100);

        log_write_formatted("Invalid limit input: %s", input_str);
        return 0;
    }

    current_limit = (uint32_t)(limit_value * 100.0f + 0.5f);

    my_printf(DEBUG_USART, "limit modified success\r\n");
    my_printf(DEBUG_USART, "Limit=%lu.%02lu\r\n", current_limit/100, current_limit%100);

    log_write_formatted("Limit modified to %lu.%02lu", current_limit/100, current_limit%100);

    return 1;
}

uint8_t save_config_to_flash(void)
{
    if(write_config_to_flash(current_ratio, current_limit))
    {
        my_printf(DEBUG_USART, "Ratio=%lu.%02lu\r\n", current_ratio/100, current_ratio%100);
        my_printf(DEBUG_USART, "Limit=%lu.%02lu\r\n", current_limit/100, current_limit%100);
        my_printf(DEBUG_USART, "save parameters to flash\r\n");
        return 1;
    }
    else
    {
        my_printf(DEBUG_USART, "config save error\r\n");
        return 0;
    }
}

uint8_t load_config_from_flash_on_startup(void)
{
    config_data_t config;

    spi_flash_buffer_read((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    if(config.magic == CONFIG_MAGIC)
    {
        current_ratio = config.ratio;
        current_limit = config.limit;

        if(config.sampling_cycle == 5 || config.sampling_cycle == 10 || config.sampling_cycle == 15)
        {
            switch(config.sampling_cycle)
            {
                case 5:  sampling_period = 50;  break;
                case 10: sampling_period = 100; break;
                case 15: sampling_period = 150; break;
            }
        }

        return 1;
    }
    else
    {
        // 
        return 0;
    }
}

/**
 * @brief 
 *
 * @return uint8_t 
 */
uint8_t read_config_from_flash(void)
{
    config_data_t config;

    // 
    spi_flash_buffer_read((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    // 
    if(config.magic == CONFIG_MAGIC)
    {
        my_printf(DEBUG_USART, "read parameters from flash\r\n");
        my_printf(DEBUG_USART, "Ratio=%lu.%02lu\r\n", config.ratio/100, config.ratio%100);
        my_printf(DEBUG_USART, "Limit=%lu.%02lu\r\n", config.limit/100, config.limit%100);
        return 1;
    }
    else
    {
        my_printf(DEBUG_USART, "read parameters from flash\r\n");
        my_printf(DEBUG_USART, "No valid config found in flash\r\n");
        return 0;
    }
}

/**
 * @brief 
 */
void start_sampling(void)
{
    // 
    bsp_adc_init();

    // 
    uint8_t cycle_from_flash = read_sampling_cycle_from_flash();

    // 
    switch(cycle_from_flash)
    {
        case 5:
            sampling_period = 50;  // 5s = 50 * 100ms
            break;
        case 10:
            sampling_period = 100; // 10s = 100 * 100ms
            break;
        case 15:
            sampling_period = 150; // 15s = 150 * 100ms
            break;
        default:
            sampling_period = 50;  // 
            break;
    }

    sampling_enabled = 1;
    sampling_counter = 0;
    led_counter = 0;

    // 
    extern uint8_t ucLed[6];
    ucLed[1] = 0;  // 

    my_printf(DEBUG_USART, "Periodic Sampling\r\n");
    my_printf(DEBUG_USART, "sample cycle:%ds\r\n", cycle_from_flash);
    log_write_formatted("Sampling started with %ds cycle", cycle_from_flash);
}

/**
 * @brief 
 */
void stop_sampling(void)
{
    sampling_enabled = 0;
    sampling_counter = 0;
    led_counter = 0;

    // 
    close_all_files();

    // 
    extern uint8_t ucLed[6];
    ucLed[0] = 0;  // 
    ucLed[1] = 0;  // 

    my_printf(DEBUG_USART, "Periodic Sampling STOP\r\n");
    log_write("Sampling stopped");
}

/**
 * @brief 
 *
 * @return uint8_t 
 */
uint8_t get_sampling_status(void)
{
    return sampling_enabled;
}

/**
 * @brief 
 *
 * @return uint32_t 
 */
uint32_t get_sampling_period(void)
{
    return sampling_period;
}

/**
 * @brief 
 */
void toggle_sampling(void)
{
    if(sampling_enabled)
    {
        stop_sampling();
    }
    else
    {
        start_sampling();
    }
}

/**
 * @brief 
 *
 * @param period_seconds 
 */
void set_sampling_period(uint8_t period_seconds)
{
    // ��֤����
    if(period_seconds != 5 && period_seconds != 10 && period_seconds != 15)
    {
        period_seconds = 5;  
    }

   
    write_sampling_cycle_to_flash(period_seconds);

    // 
    switch(period_seconds)
    {
        case 5:
            sampling_period = 50;  // 5s = 50 * 100ms
            break;
        case 10:
            sampling_period = 100; // 10s = 100 * 100ms
            break;
        case 15:
            sampling_period = 150; // 15s = 150 * 100ms
            break;
    }

    // 
    sampling_counter = 0;

    // 
    my_printf(DEBUG_USART, "sample cycle adjust:%ds\r\n", period_seconds);
}

/**
 * @brief 
 *
 * @return uint8_t 
 */
uint8_t write_device_id_to_flash(void)
{
    device_id_t device_data;

    // 
    strcpy(device_data.device_id, "2025-CMIC-2025916750");
    device_data.magic = DEVICE_ID_MAGIC;

    // 
    spi_flash_sector_erase(DEVICE_ID_FLASH_ADDR);

    // 
    spi_flash_buffer_write((uint8_t*)&device_data, DEVICE_ID_FLASH_ADDR, sizeof(device_id_t));

    return 1;
}

/**
 * @brief 
 *
 * @return uint8_t 
 */
uint8_t read_and_display_device_id(void)
{
    device_id_t device_data;

    // 
    spi_flash_buffer_read((uint8_t*)&device_data, DEVICE_ID_FLASH_ADDR, sizeof(device_id_t));

    // 
    if(device_data.magic == DEVICE_ID_MAGIC)
    {
        my_printf(DEBUG_USART, "Device_ID:%s\r\n", device_data.device_id);
        return 1;
    }
    else
    {
        my_printf(DEBUG_USART, "Device_ID:Not Found\r\n");
        return 0;
    }
}

/**
 * @brief 
 *
 * @param ratio 
 * @param limit 
 * @return uint8_t 
 */
uint8_t write_config_to_flash(uint32_t ratio, uint32_t limit)
{
    config_data_t config;

    spi_flash_buffer_read((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    config.ratio = ratio;
    config.limit = limit;

    if(config.magic != CONFIG_MAGIC)
    {
        config.sampling_cycle = 5;  
    }

    config.magic = CONFIG_MAGIC;

    
    spi_flash_sector_erase(CONFIG_FLASH_ADDR);

    
    spi_flash_buffer_write((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    return 1;
}

/**
 * @brief 
 *
 * @param cycle_seconds 
 * @return uint8_t 
 */
uint8_t write_sampling_cycle_to_flash(uint8_t cycle_seconds)
{
    config_data_t config;

    spi_flash_buffer_read((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    if(config.magic != CONFIG_MAGIC)
    {
        config.ratio = 199;   // 1.99
        config.limit = 1011;  // 10.11
    }

    config.sampling_cycle = cycle_seconds;
    config.magic = CONFIG_MAGIC;

    spi_flash_sector_erase(CONFIG_FLASH_ADDR);

    
    spi_flash_buffer_write((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    return 1;
}

/**
 * @brief 
 *
 * @return uint8_t 
 */
uint8_t read_sampling_cycle_from_flash(void)
{
    config_data_t config;

    
    spi_flash_buffer_read((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    if(config.magic == CONFIG_MAGIC &&
       (config.sampling_cycle == 5 || config.sampling_cycle == 10 || config.sampling_cycle == 15))
    {
        return config.sampling_cycle;
    }
    else
    {
        return 5;  //
    }
}

/**
 * @brief 
*
 * @return uint32_t 
*/
uint32_t rtc_to_unix_timestamp(void)
{
    extern rtc_parameter_struct rtc_initpara;
    rtc_current_time_get(&rtc_initpara);

    uint8_t year = ((rtc_initpara.year >> 4) * 10) + (rtc_initpara.year & 0x0F);
    uint8_t month = ((rtc_initpara.month >> 4) * 10) + (rtc_initpara.month & 0x0F);
    uint8_t date = ((rtc_initpara.date >> 4) * 10) + (rtc_initpara.date & 0x0F);
    uint8_t hour = ((rtc_initpara.hour >> 4) * 10) + (rtc_initpara.hour & 0x0F);
    uint8_t minute = ((rtc_initpara.minute >> 4) * 10) + (rtc_initpara.minute & 0x0F);
    uint8_t second = ((rtc_initpara.second >> 4) * 10) + (rtc_initpara.second & 0x0F);

    uint16_t full_year = 2000 + year;

    uint16_t days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    uint32_t days = 0;

    
    for(uint16_t y = 1970; y < full_year; y++)
    {
        if((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0))
            days += 366;  // 
        else
            days += 365;  // 
    }

    
    for(uint8_t m = 1; m < month; m++)
    {
        days += days_in_month[m-1];
        
				if(m == 2 && ((full_year % 4 == 0 && full_year % 100 != 0) || (full_year % 400 == 0)))
            days += 1;
    }

    
    days += (date - 1);

    uint32_t timestamp = days * 86400UL + hour * 3600UL + minute * 60UL + second;

    
    if(timestamp >= 28800UL)
    {
        timestamp -= 28800UL;
    }

    return timestamp;
}

/**
 * @brief 
 *
 * @param file 
 * @param buffer 
 * @param buffer_size 
 * @return uint8_t 
 */
uint8_t read_line_from_file(FIL* file, char* buffer, uint16_t buffer_size)
{
    UINT bytes_read;
    char ch;
    uint16_t index = 0;
    uint8_t found_line_ending = 0;

    while(index < (buffer_size - 1))
    {
        
        FRESULT result = f_read(file, &ch, 1, &bytes_read);
        if(result != FR_OK)
        {
            break;
        }
        if(bytes_read == 0)
        {
            break; // EOF
        }

        
				if(ch == '\n' || ch == '\r')
        {
            found_line_ending = 1;
            
  					if(ch == '\r')
            {
               
                DWORD current_pos = f_tell(file);
                UINT temp_bytes_read;
                if(f_read(file, &ch, 1, &temp_bytes_read) == FR_OK && temp_bytes_read == 1)
                {
                    if(ch != '\n')
                    {
                      
                        f_lseek(file, current_pos);
                    }
                }
            }
            break;
        }

        buffer[index++] = ch;
    }

    buffer[index] = '\0';

    
    if(found_line_ending || index > 0)
    {
        return 1;  
    }
    else
    {
        return 0;  
		}
}

/**
 * @brief 
 *
 * @return uint8_t 
 */
uint8_t read_config_from_tf_card(void)
{
    FIL config_file;
    char line_buffer[128];
    uint8_t config_found = 0;

    // 确保文件系统已挂载
    if(!init_filesystem())
    {
        my_printf(DEBUG_USART, "TF card not mounted\r\n");
        return 0;
    }

    // 尝试打开配置文件
    FRESULT result = f_open(&config_file, "0:/config.ini", FA_READ);
    if(result != FR_OK)
    {
        my_printf(DEBUG_USART, "config.ini file not found.\r\n");
        return 0;  // 文件不存在或无法打开
    }

    // 逐行读取配置文件
    float ratio_value = -1.0f;
    float limit_value = -1.0f;

    while(read_line_from_file(&config_file, line_buffer, sizeof(line_buffer)))
    {
        // 清理行内容：去除首尾的[]符号和空格
        char cleaned_line[128];
        char* src = line_buffer;
        char* dst = cleaned_line;

        // 跳过开头的[和空格
        while(*src == '[' || *src == ' ' || *src == '\t') src++;

        // 复制内容，跳过结尾的]和空格
        while(*src != '\0' && *src != ']' && *src != '\r' && *src != '\n')
        {
            *dst++ = *src++;
        }
        *dst = '\0';

        // 去除尾部空格
        while(dst > cleaned_line && (*(dst-1) == ' ' || *(dst-1) == '\t'))
        {
            *(--dst) = '\0';
        }

        // 跳过空行和注释
        if(strlen(cleaned_line) == 0 || cleaned_line[0] == '#' || cleaned_line[0] == ';')
            continue;

        // 跳过节标题
        if(strcmp(cleaned_line, "Ratio") == 0 || strcmp(cleaned_line, "ratio") == 0 ||
           strcmp(cleaned_line, "Limit") == 0 || strcmp(cleaned_line, "limit") == 0)
            continue;

        // 查找配置项
        if(strstr(cleaned_line, "Ch0") != NULL && strstr(cleaned_line, "=") != NULL)
        {
            char* value_start = strstr(cleaned_line, "=");
            if(value_start != NULL)
            {
                value_start++; // 跳过'='
                // 跳过空格
                while(*value_start == ' ' || *value_start == '\t') value_start++;
                float config_value = atof(value_start);

                // 根据数值范围判断是ratio还是limit
                if(config_value >= 0.0f && config_value <= 100.0f && ratio_value < 0.0f)
                {
                    // 第一个在范围内的值作为ratio
                    ratio_value = config_value;
                    current_ratio = (uint32_t)(config_value * 100.0f + 0.5f);
                    config_found = 1;
                }
                else if(config_value >= 0.0f && config_value <= 200.0f && limit_value < 0.0f)
                {
                    // 第一个在范围内的值作为limit
                    limit_value = config_value;
                    current_limit = (uint32_t)(config_value * 100.0f + 0.5f);
                    config_found = 1;
                }
            }
        }
    }

    f_close(&config_file);

    if(config_found)
    {
       
        if(ratio_value >= 0.0f)
        {
            my_printf(DEBUG_USART, "Ratio=%.1f\r\n", ratio_value);
        }
        if(limit_value >= 0.0f)
        {
            my_printf(DEBUG_USART, "Limit=%.0f\r\n", limit_value);
        }
        my_printf(DEBUG_USART, "config read success\r\n");
        return 1;
    }

    return 0;
}

/**
 * @brief ��������
 *
 * @param cmd_str �����ַ���
 */
void process_command(char* cmd_str)
{
    // �Ƴ�ĩβ�Ļ��з�
    char* newline = strstr(cmd_str, "\r\n");
    if(newline) *newline = '\0';
    newline = strstr(cmd_str, "\n");
    if(newline) *newline = '\0';
    newline = strstr(cmd_str, "\r");
    if(newline) *newline = '\0';

    // ��¼���յ�����
    if(strlen(cmd_str) > 0)
    {
        if(pre_test_logging)
        {
            // testǰ�洢��testǰ������
            char formatted_msg[256];
            snprintf(formatted_msg, sizeof(formatted_msg), "IN: %s", cmd_str);
            add_to_pre_test_buffer(formatted_msg);
        }
        else
        {
            // test��������־��¼
            log_write_formatted("IN: %s", cmd_str);
        }
    }

    // ����Ƿ��ڵȴ�ratio����
    if(waiting_for_ratio_input)
    {
        waiting_for_ratio_input = 0;  // ��λ��־
        process_ratio_input(cmd_str);
        return;
    }

   
    if(waiting_for_limit_input)
    {
        waiting_for_limit_input = 0; 
        process_limit_input(cmd_str);
        return;
    }

  
    if(waiting_for_rtc_input)
    {
        waiting_for_rtc_input = 0; 
        if(parse_and_set_rtc_time(cmd_str))
        {
            my_printf(DEBUG_USART, "RTC Config success\r\n");
            char current_time[32];
            rtc_get_time_string(current_time, sizeof(current_time));
            my_printf(DEBUG_USART, "Time:%s\r\n", current_time);
            log_write_formatted("RTC Config Success: %s", current_time);
        }
        else
        {
            my_printf(DEBUG_USART, "RTC Config error\r\n");
            my_printf(DEBUG_USART, "Format: YYYY MM DD HH:MM:SS\r\n");
            my_printf(DEBUG_USART, "Example: 2025 06 15 10:10:10\r\n");
            log_write_formatted("RTC Config Failed: %s", cmd_str);
        }
        return;
    }

 
    if(strcmp(cmd_str, "test") == 0)
    {
  
        my_printf(DEBUG_USART, "=====system selftest=====\r\n");
        log_write("Command: test");

     
        test_flash();

       
        if(test_tf_card())
        {
        
					set_boot_count_and_save(0);

            
            if(!is_log_initialized())
            {
                set_log_initialized();

               

               
                auto_log_enabled = 0;

                log_close();
                log_start_session();  

              
                flush_pre_test_buffer_to_log();

                
								log_write("Test successful - System first initialization");
                log_write("Flash detection completed");
                log_write("TF card detection completed");
                log_write("System ready for operation");
                log_write("=== log0.txt completed ===");

                
                log_close();

                
                increment_boot_count();
                log_start_session(); 

              
                auto_log_enabled = 1;

                my_printf(DEBUG_USART, "System initialized. Normal operation logging started.\r\n");
            }
            else
            {
               
                my_printf(DEBUG_USART, "System test completed successfully\r\n");
            }
        }
        else
        {
            my_printf(DEBUG_USART, "System test failed\r\n");
            my_printf(DEBUG_USART, "=====system selftest end=====\r\n");

            // Stop pre_test_logging after first test failure
            // This ensures log0.txt only contains content up to the first test failure
            if(pre_test_logging)
            {
                pre_test_logging = 0;  // Stop recording to pre_test_buffer
                my_printf(DEBUG_USART, "Pre-test logging stopped after test failure\r\n");
            }
            return;  // Exit early to avoid duplicate "selftest end" message
        }

        my_printf(DEBUG_USART, "=====system selftest end=====\r\n");
    }
    // Check for ratio command
    else if(strcmp(cmd_str, "ratio") == 0)
    {
        my_printf(DEBUG_USART, "Ratio=%lu.%02lu\r\n", current_ratio/100, current_ratio%100);
        my_printf(DEBUG_USART, "Input value(0~100):\r\n");
        waiting_for_ratio_input = 1;  // Set flag to wait for input
    }
    // Check for limit command
    else if(strcmp(cmd_str, "limit") == 0)
    {
        my_printf(DEBUG_USART, "Limit=%lu.%02lu\r\n", current_limit/100, current_limit%100);
        my_printf(DEBUG_USART, "Input value(0~200):\r\n");
        waiting_for_limit_input = 1;  // Set flag to wait for input
    }
    // Check for conf command
    else if(strcmp(cmd_str, "conf") == 0)
    {
        if(read_config_from_tf_card())
        {
            // Success message already printed in read_config_from_tf_card()
        }
        else
        {
            my_printf(DEBUG_USART, "conf.ini file not found, using default values\r\n");
            my_printf(DEBUG_USART, "Default Ratio: 1.00\r\n");
            my_printf(DEBUG_USART, "Default Limit: 2.50\r\n");
            my_printf(DEBUG_USART, "Please create config.ini file on TF card with:\r\n");
            my_printf(DEBUG_USART, "[Ratio]\r\nCh0=1.00\r\n[Limit]\r\nCh0=2.50\r\n");
        }
    }
    // Check for config save command
    else if(strcmp(cmd_str, "config save") == 0)
    {
        save_config_to_flash();
    }
    // Check for config read command
    else if(strcmp(cmd_str, "config read") == 0)
    {
        read_config_from_flash();
    }
    // Check for start command
    else if(strcmp(cmd_str, "start") == 0)
    {
        start_sampling();
        log_write("Command: start - Sampling started");
    }
    // Check for stop command
    else if(strcmp(cmd_str, "stop") == 0)
    {
        stop_sampling();
        log_write("Command: stop - Sampling stopped");
    }
    // Check for hide command
    else if(strcmp(cmd_str, "hide") == 0)
    {
        hide_mode = 1;  // Enable hide mode
        my_printf(DEBUG_USART, "Hide mode ON - HEX output enabled\r\n");
        log_write("Command: hide - Hide mode enabled");
    }
    // Check for unhide command
    else if(strcmp(cmd_str, "unhide") == 0)
    {
        hide_mode = 0;  // Disable hide mode
        my_printf(DEBUG_USART, "Hide mode OFF - Normal output enabled\r\n");
        log_write("Command: unhide - Hide mode disabled");
    }
    // Check for RTC now command
    else if(strcmp(cmd_str, "RTC now") == 0)
    {
        char current_time[32];
        rtc_get_time_string(current_time, sizeof(current_time));
        my_printf(DEBUG_USART, "Current Time %s\r\n", current_time);
    }
    // Check for RTC Config command (two-step process)
    else if(strcmp(cmd_str, "RTC Config") == 0)
    {
        waiting_for_rtc_input = 1;
        my_printf(DEBUG_USART, "Input Datatime\r\n");
    }
    // Check for log close command (format: log[N] close)
    else if(strncmp(cmd_str, "log", 3) == 0 && strstr(cmd_str, "close") != NULL)
    {
        int log_number;
        if(sscanf(cmd_str, "log%d close", &log_number) == 1)
        {
            if(log_number >= 0 && log_number <= 3)  // Limit log number range
            {
                close_log_file_by_number((uint32_t)log_number);
            }
            else
            {
                my_printf(DEBUG_USART, "Invalid log number. Use log0-log3\r\n");
            }
        }
        else
        {
            my_printf(DEBUG_USART, "Invalid format. Use: log[N] close (e.g., log0 close)\r\n");
        }
    }
    // Check for log open command (format: log[N] open)
    else if(strncmp(cmd_str, "log", 3) == 0 && strstr(cmd_str, "open") != NULL)
    {
        int log_number;
        if(sscanf(cmd_str, "log%d open", &log_number) == 1)
        {
            if(log_number >= 0 && log_number <= 3)  // Support log0-log3
            {
                open_log_file_by_number((uint32_t)log_number);
            }
            else
            {
                my_printf(DEBUG_USART, "Invalid log number. Use log0-log3\r\n");
            }
        }
        else
        {
            my_printf(DEBUG_USART, "Invalid format. Use: log[N] open (e.g., log0 open)\r\n");
        }
    }
    // Check for RS485 test command
    else if(strcmp(cmd_str, "rs485 test") == 0)
    {
        rs485_self_test();
    }
    // Check for RS485 loopback test command
    else if(strcmp(cmd_str, "rs485 loopback") == 0)
    {
        rs485_loopback_test();
    }
    // Check for RS485 ping command
    else if(strncmp(cmd_str, "rs485 ping", 10) == 0)
    {
        uint8_t target_addr = 0x02; // 默认地址
        if(strlen(cmd_str) > 11)
        {
            // 解析目标地址
            sscanf(cmd_str + 11, "%hhx", &target_addr);
        }
        rs485_communication_test(target_addr);
    }
    // Check for RS485 send command
    else if(strncmp(cmd_str, "rs485 send", 10) == 0)
    {
        uint8_t target_addr = 0x02;
        char message[64] = "Test Message";

        if(strlen(cmd_str) > 11)
        {
            // 解析地址和消息: rs485 send 02 Hello
            char* addr_str = cmd_str + 11;
            char* msg_str = strchr(addr_str, ' ');

            if(msg_str != NULL)
            {
                *msg_str = '\0';
                msg_str++;
                sscanf(addr_str, "%hhx", &target_addr);
                strncpy(message, msg_str, sizeof(message) - 1);
                message[sizeof(message) - 1] = '\0';
            }
            else
            {
                sscanf(addr_str, "%hhx", &target_addr);
            }
        }

        if(rs485_send_data(target_addr, (uint8_t*)message, strlen(message)))
        {
            my_printf(DEBUG_USART, "RS485 message sent to 0x%02X: %s\r\n", target_addr, message);
        }
        else
        {
            my_printf(DEBUG_USART, "RS485 send failed\r\n");
        }
    }
    // Check for RS485 stats command
    else if(strcmp(cmd_str, "rs485 stats") == 0)
    {
        rs485_print_statistics();
    }
    // Check for RS485 clear command
    else if(strcmp(cmd_str, "rs485 clear") == 0)
    {
        rs485_clear_statistics();
        my_printf(DEBUG_USART, "RS485 statistics cleared\r\n");
    }
    // Check for RS485 address command
    else if(strncmp(cmd_str, "rs485 addr", 10) == 0)
    {
        if(strlen(cmd_str) > 11)
        {
            uint8_t new_addr;
            sscanf(cmd_str + 11, "%hhx", &new_addr);
            rs485_set_local_address(new_addr);
            my_printf(DEBUG_USART, "RS485 local address set to 0x%02X\r\n", new_addr);
        }
        else
        {
            uint8_t current_addr = rs485_get_local_address();
            my_printf(DEBUG_USART, "RS485 local address: 0x%02X\r\n", current_addr);
        }
    }
    else if(strlen(cmd_str) > 0)
    {
        my_printf(DEBUG_USART, "Unknown command: %s\r\n", cmd_str);
        my_printf(DEBUG_USART, "Available commands: test, conf, ratio, limit, config save, config read, start, stop, hide, unhide, RTC Config, RTC now, format, log[N] open, log[N] close\r\n");
        my_printf(DEBUG_USART, "RS485 commands: rs485 test, rs485 loopback, rs485 ping [addr], rs485 send [addr] [message], rs485 stats, rs485 clear, rs485 addr [addr]\r\n");
    }
}

/**
 * @brief UART������
 *
 * ��rx_flag����ʱ�������յ���UART����
 */
void uart_task(void)
{
    if(!rx_flag) return;

   
    rx_flag = 0;

    
    process_command((char*)uart_dma_buffer);

    memset(uart_dma_buffer, 0, 256);
}

/**
 * @brief 
 */
void sampling_task(void)
{
    if(!sampling_enabled) return;

    extern uint8_t ucLed[6];
    extern uint16_t adc_value[1];

    
    adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL);

    
    uint32_t timeout = 10000;
    while(!adc_flag_get(ADC0, ADC_FLAG_EOC) && timeout > 0) {
        timeout--;
    }

    if(timeout > 0) {
        adc_value[0] = adc_routine_data_read(ADC0);
        adc_flag_clear(ADC0, ADC_FLAG_EOC);
    } else {
       
        my_printf(DEBUG_USART, "ADC timeout\r\n");
    }

    
    led_counter++;
    if(led_counter >= 5)
    {
        led_counter = 0;
        ucLed[0] = !ucLed[0];  
    }

  
    sampling_counter++;
    if(sampling_counter >= sampling_period)
    {
        sampling_counter = 0;

        
        uint64_t temp_calc = (uint64_t)adc_value[0] * current_ratio * 3300;
        uint32_t displayed_voltage_mv = temp_calc / (4096 * 100); 
        
        char current_time[32];
        rtc_get_time_string(current_time, sizeof(current_time));

       
        uint32_t limit_mv = (current_limit * 1000) / 100;  
        uint8_t is_over_limit = (displayed_voltage_mv > limit_mv);

        if(is_over_limit)
        {
            
            ucLed[1] = 1;  
        }
        else
        {
            
            ucLed[1] = 0;  
        }
       
        if(is_over_limit)
        {
           
            char voltage_str[32];
            snprintf(voltage_str, sizeof(voltage_str), "%lu.%02luV", displayed_voltage_mv/1000, (displayed_voltage_mv%1000)/10);
            write_data_to_file(current_time, voltage_str, STORAGE_MODE_OVERLIMIT);
        }
        else
        {
           
            char voltage_str[32];
            snprintf(voltage_str, sizeof(voltage_str), "%lu.%02luV", displayed_voltage_mv/1000, (displayed_voltage_mv%1000)/10);
            write_sample_to_file(current_time, voltage_str);
        }

        if(is_over_limit)
        {
            my_printf(DEBUG_USART, "%s ch0=%lu.%02luV OverLimit(%lu.%02luV)\r\n",
                     current_time,
                     displayed_voltage_mv/1000, (displayed_voltage_mv%1000)/10,
                     limit_mv/1000, (limit_mv%1000)/10);
        }
        else
        {
            my_printf(DEBUG_USART, "%s ch0=%lu.%02luV\r\n", current_time, displayed_voltage_mv/1000, (displayed_voltage_mv%1000)/10);
        }
    }
}

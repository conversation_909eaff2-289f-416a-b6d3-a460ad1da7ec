# 串口模块完整使用指南

## 概述

本系统提供了完整的串口通信功能，支持数据发送、接收、命令解析和自动日志记录。主要用于调试输出、命令输入和系统交互。

## 核心文件和函数位置

### 主要文件
- **usart_app.c** - 串口应用层实现
- **usart_app.h** - 串口应用层头文件
- **bsp_usart.c** - 串口底层驱动
- **bsp_usart.h** - 串口底层驱动头文件

## 1. 串口初始化

### 1.1 底层硬件初始化
```c
// 位置：bsp_usart.c
void bsp_usart_init(void)
```
**作用**：初始化串口硬件，配置波特率、数据位、停止位等参数

**调用位置**：main.c中的系统初始化阶段
```c
void main(void) {
    // 其他硬件初始化...
    bsp_usart_init();  // 串口硬件初始化
    // 继续其他初始化...
}
```

**内部配置**：
- 波特率：115200
- 数据位：8位
- 停止位：1位
- 校验位：无
- 流控：无

### 1.2 DMA配置
```c
// 位置：bsp_usart.c
static void usart_dma_config(void)
```
**作用**：配置串口DMA接收，实现非阻塞数据接收

**特性**：
- 自动接收数据到缓冲区
- 接收完成后触发中断
- 支持连续接收

## 2. 数据发送功能

### 2.1 格式化输出函数（推荐使用）
```c
// 位置：usart_app.c
int my_printf(uint32_t usart_periph, const char *format, ...)
```
**作用**：类似printf的格式化输出，支持自动日志记录

**调用示例**：
```c
// 基础输出
my_printf(DEBUG_USART, "System started\r\n");

// 格式化输出
uint32_t voltage = 3300;  // 3.3V in mV
my_printf(DEBUG_USART, "Voltage: %lu.%03luV\r\n", voltage/1000, voltage%1000);

// 多参数输出
uint8_t temp = 25;
uint16_t humidity = 60;
my_printf(DEBUG_USART, "Temp: %dC, Humidity: %d%%\r\n", temp, humidity);

// 十六进制输出
uint32_t flash_id = 0xC84013;
my_printf(DEBUG_USART, "Flash ID: 0x%06lX\r\n", flash_id);
```

**自动日志记录**：
- 所有通过`my_printf`输出的内容会自动记录到日志系统
- 格式：`OUT: 内容`
- 根据系统状态决定记录到pre_test_buffer或日志文件

### 2.2 底层发送函数
```c
// 位置：bsp_usart.c
void usart_data_transmit(uint32_t usart_periph, uint8_t data)
```
**作用**：发送单个字节数据

**调用示例**：
```c
// 发送单个字符
usart_data_transmit(DEBUG_USART, 'A');

// 发送字符串（手动循环）
char* message = "Hello World";
for(int i = 0; message[i] != '\0'; i++) {
    usart_data_transmit(DEBUG_USART, message[i]);
    while(RESET == usart_flag_get(DEBUG_USART, USART_FLAG_TBE));
}
```

### 2.3 状态输出函数
```c
// 位置：usart_app.c（自定义函数）
void print_system_status(void)
```
**作用**：输出系统状态信息

**实现示例**：
```c
void print_system_status(void) {
    my_printf(DEBUG_USART, "=== System Status ===\r\n");
    my_printf(DEBUG_USART, "CPU Freq: %lu MHz\r\n", SystemCoreClock/1000000);
    my_printf(DEBUG_USART, "Free RAM: %lu bytes\r\n", get_free_ram());
    my_printf(DEBUG_USART, "Uptime: %lu seconds\r\n", get_uptime());
    my_printf(DEBUG_USART, "==================\r\n");
}
```

## 3. 数据接收功能

### 3.1 DMA接收缓冲区
```c
// 位置：usart_app.c
uint8_t uart_dma_buffer[512] = {0};  // DMA接收缓冲区
__IO uint8_t rx_flag = 0;            // 接收完成标志
```

### 3.2 接收中断处理
```c
// 位置：bsp_usart.c
void DMA1_Channel4_IRQHandler(void)
```
**作用**：DMA接收完成中断处理函数

**处理流程**：
1. 检查DMA传输完成标志
2. 设置`rx_flag = 1`
3. 清除中断标志
4. 重新启动DMA接收

### 3.3 接收数据处理
```c
// 位置：usart_app.c
void uart_task(void)
```
**作用**：处理接收到的串口数据

**调用位置**：主循环中的调度器
```c
void scheduler_run(void) {
    uart_task();        // 处理串口数据
    sampling_task();    // 处理采样任务
    // 其他任务...
}
```

**处理流程**：
1. 检查`rx_flag`标志
2. 如果有数据，调用`process_command()`处理
3. 清空接收缓冲区
4. 重置接收标志

## 4. 命令解析系统

### 4.1 命令处理主函数
```c
// 位置：usart_app.c
void process_command(char* cmd_str)
```
**作用**：解析和执行串口接收的命令

**支持的命令类型**：
- 系统测试命令：`test`
- 日志管理命令：`log0 open`, `log0 close`, `log1 open`, `log1 close`
- 参数设置命令：`ratio`, `limit`
- 采样控制命令：`start`, `stop`
- 配置管理命令：`config save`, `config read`, `conf`
- 时间设置命令：RTC时间格式

### 4.2 命令自动记录
```c
// 位置：usart_app.c（在process_command函数内）
if(strlen(cmd_str) > 0) {
    if(pre_test_logging) {
        // 记录到pre_test_buffer
        char formatted_msg[256];
        snprintf(formatted_msg, sizeof(formatted_msg), "IN: %s", cmd_str);
        add_to_pre_test_buffer(formatted_msg);
    } else {
        // 记录到日志文件
        log_write_formatted("IN: %s", cmd_str);
    }
}
```

### 4.3 具体命令处理示例

#### 4.3.1 测试命令处理
```c
if(strcmp(cmd_str, "test") == 0) {
    my_printf(DEBUG_USART, "=====system selftest=====\r\n");
    log_write("Command: test");
    
    // 测试Flash
    test_flash();
    
    // 测试TF卡
    if(test_tf_card()) {
        // 测试成功处理...
        my_printf(DEBUG_USART, "System test completed successfully\r\n");
    } else {
        my_printf(DEBUG_USART, "System test failed\r\n");
    }
    
    my_printf(DEBUG_USART, "=====system selftest end=====\r\n");
}
```

#### 4.3.2 参数设置命令处理
```c
if(strcmp(cmd_str, "ratio") == 0) {
    waiting_for_ratio_input = 1;
    my_printf(DEBUG_USART, "Ratio=%lu.%02lu\r\n", current_ratio/100, current_ratio%100);
    my_printf(DEBUG_USART, "Input value(0~100):\r\n");
}

// 在等待输入状态下处理
if(waiting_for_ratio_input) {
    waiting_for_ratio_input = 0;
    if(process_ratio_input(cmd_str)) {
        my_printf(DEBUG_USART, "ratio modified success\r\n");
        log_write_formatted("Ratio modified to %lu.%02lu", current_ratio/100, current_ratio%100);
    } else {
        my_printf(DEBUG_USART, "ratio invalid\r\n");
        log_write_formatted("Invalid ratio input: %s", cmd_str);
    }
}
```

#### 4.3.3 日志管理命令处理
```c
if(strncmp(cmd_str, "log", 3) == 0) {
    if(strstr(cmd_str, "open") != NULL) {
        // 解析日志编号
        uint32_t log_number = extract_log_number(cmd_str);
        if(open_log_file_by_number(log_number)) {
            my_printf(DEBUG_USART, "log%lu.txt opened successfully\r\n", log_number);
        } else {
            my_printf(DEBUG_USART, "Failed to open log%lu.txt\r\n", log_number);
        }
    } else if(strstr(cmd_str, "close") != NULL) {
        uint32_t log_number = extract_log_number(cmd_str);
        if(close_log_file_by_number(log_number)) {
            my_printf(DEBUG_USART, "log%lu.txt closed successfully\r\n", log_number);
        } else {
            my_printf(DEBUG_USART, "Failed to close log%lu.txt\r\n", log_number);
        }
    }
}
```

## 5. 参数输入处理

### 5.1 比例参数输入处理
```c
// 位置：usart_app.c
uint8_t process_ratio_input(char* input_str)
```
**作用**：处理比例参数输入，范围0~100

**调用示例**：
```c
// 用户输入"1.99"
if(process_ratio_input("1.99")) {
    // 输入有效，current_ratio = 199 (1.99 * 100)
    my_printf(DEBUG_USART, "Ratio set to 1.99\r\n");
} else {
    // 输入无效
    my_printf(DEBUG_USART, "Invalid ratio value\r\n");
}
```

### 5.2 限值参数输入处理
```c
// 位置：usart_app.c
uint8_t process_limit_input(char* input_str)
```
**作用**：处理限值参数输入，范围0~200

**调用示例**：
```c
// 用户输入"10.5"
if(process_limit_input("10.5")) {
    // 输入有效，current_limit = 1050 (10.5 * 100)
    my_printf(DEBUG_USART, "Limit set to 10.50\r\n");
} else {
    // 输入无效
    my_printf(DEBUG_USART, "Invalid limit value\r\n");
}
```

### 5.3 RTC时间输入处理
```c
// 位置：usart_app.c
uint8_t parse_and_set_rtc_time(char* time_str)
```
**作用**：解析并设置RTC时间

**输入格式**：`YYYY MM DD HH:MM:SS`

**调用示例**：
```c
// 用户输入"2025 08 04 14:30:00"
if(parse_and_set_rtc_time("2025 08 04 14:30:00")) {
    my_printf(DEBUG_USART, "RTC time set successfully\r\n");
    log_write_formatted("RTC Config Success: 2025-08-04 14:30:00");
} else {
    my_printf(DEBUG_USART, "Invalid time format\r\n");
    my_printf(DEBUG_USART, "Format: YYYY MM DD HH:MM:SS\r\n");
    my_printf(DEBUG_USART, "Example: 2025 06 15 10:10:10\r\n");
}
```

## 6. 状态管理

### 6.1 输入等待状态
```c
// 位置：usart_app.c
static uint8_t waiting_for_ratio_input = 0;  // 等待比例输入
static uint8_t waiting_for_limit_input = 0;  // 等待限值输入
static uint8_t waiting_for_rtc_input = 0;    // 等待RTC输入
```

### 6.2 状态检查和切换
```c
// 检查当前等待状态
uint8_t get_input_waiting_status(void) {
    if(waiting_for_ratio_input) return INPUT_WAITING_RATIO;
    if(waiting_for_limit_input) return INPUT_WAITING_LIMIT;
    if(waiting_for_rtc_input) return INPUT_WAITING_RTC;
    return INPUT_WAITING_NONE;
}

// 清除所有等待状态
void clear_all_waiting_status(void) {
    waiting_for_ratio_input = 0;
    waiting_for_limit_input = 0;
    waiting_for_rtc_input = 0;
}
```

## 7. 错误处理和调试

### 7.1 串口状态检查
```c
// 检查串口发送状态
uint8_t check_usart_tx_status(void) {
    if(usart_flag_get(DEBUG_USART, USART_FLAG_TBE)) {
        return USART_TX_READY;
    } else {
        return USART_TX_BUSY;
    }
}

// 检查串口接收状态
uint8_t check_usart_rx_status(void) {
    if(rx_flag) {
        return USART_RX_DATA_READY;
    } else {
        return USART_RX_NO_DATA;
    }
}
```

### 7.2 调试输出函数
```c
// 输出串口统计信息
void print_usart_statistics(void) {
    static uint32_t tx_count_total = 0;
    static uint32_t rx_count_total = 0;
    
    my_printf(DEBUG_USART, "=== USART Statistics ===\r\n");
    my_printf(DEBUG_USART, "TX Count: %lu\r\n", tx_count_total);
    my_printf(DEBUG_USART, "RX Count: %lu\r\n", rx_count_total);
    my_printf(DEBUG_USART, "RX Flag: %s\r\n", rx_flag ? "Set" : "Clear");
    my_printf(DEBUG_USART, "=====================\r\n");
}

// 输出接收缓冲区内容（调试用）
void print_rx_buffer_hex(void) {
    my_printf(DEBUG_USART, "RX Buffer (HEX): ");
    for(int i = 0; i < 32; i++) {  // 只显示前32字节
        my_printf(DEBUG_USART, "%02X ", uart_dma_buffer[i]);
    }
    my_printf(DEBUG_USART, "\r\n");
}
```

## 8. 高级功能

### 8.1 命令历史记录
```c
#define CMD_HISTORY_SIZE 10
static char cmd_history[CMD_HISTORY_SIZE][64];
static uint8_t history_index = 0;

// 添加命令到历史记录
void add_to_command_history(const char* cmd) {
    strcpy(cmd_history[history_index], cmd);
    history_index = (history_index + 1) % CMD_HISTORY_SIZE;
}

// 显示命令历史
void show_command_history(void) {
    my_printf(DEBUG_USART, "=== Command History ===\r\n");
    for(int i = 0; i < CMD_HISTORY_SIZE; i++) {
        if(strlen(cmd_history[i]) > 0) {
            my_printf(DEBUG_USART, "%d: %s\r\n", i+1, cmd_history[i]);
        }
    }
    my_printf(DEBUG_USART, "====================\r\n");
}
```

### 8.2 命令自动补全
```c
// 命令列表
const char* command_list[] = {
    "test", "start", "stop", "ratio", "limit", 
    "config save", "config read", "conf",
    "log0 open", "log0 close", "log1 open", "log1 close"
};

// 查找匹配的命令
void find_matching_commands(const char* partial_cmd) {
    my_printf(DEBUG_USART, "Possible commands:\r\n");
    
    for(int i = 0; i < sizeof(command_list)/sizeof(command_list[0]); i++) {
        if(strncmp(partial_cmd, command_list[i], strlen(partial_cmd)) == 0) {
            my_printf(DEBUG_USART, "  %s\r\n", command_list[i]);
        }
    }
}
```

### 8.3 批量命令执行
```c
// 执行多个命令
void execute_command_batch(const char* commands[], uint8_t count) {
    my_printf(DEBUG_USART, "=== Executing %d commands ===\r\n", count);
    
    for(uint8_t i = 0; i < count; i++) {
        my_printf(DEBUG_USART, "Command %d: %s\r\n", i+1, commands[i]);
        process_command((char*)commands[i]);
        delay_ms(100);  // 命令间延时
    }
    
    my_printf(DEBUG_USART, "=== Batch execution complete ===\r\n");
}

## 9. 性能优化

### 9.1 发送缓冲区优化
```c
#define TX_BUFFER_SIZE 512
static char tx_buffer[TX_BUFFER_SIZE];
static uint16_t tx_buffer_pos = 0;

// 缓冲发送数据
void buffered_printf(const char *format, ...) {
    va_list args;
    char temp_buffer[256];

    va_start(args, format);
    int len = vsnprintf(temp_buffer, sizeof(temp_buffer), format, args);
    va_end(args);

    if(tx_buffer_pos + len < TX_BUFFER_SIZE) {
        strcpy(&tx_buffer[tx_buffer_pos], temp_buffer);
        tx_buffer_pos += len;
    } else {
        flush_tx_buffer();
        strcpy(tx_buffer, temp_buffer);
        tx_buffer_pos = len;
    }
}

// 刷新发送缓冲区
void flush_tx_buffer(void) {
    if(tx_buffer_pos > 0) {
        for(uint16_t i = 0; i < tx_buffer_pos; i++) {
            usart_data_transmit(DEBUG_USART, tx_buffer[i]);
            while(RESET == usart_flag_get(DEBUG_USART, USART_FLAG_TBE));
        }
        tx_buffer_pos = 0;
    }
}
```

### 9.2 中断优化
```c
// 优化的DMA中断处理
void DMA1_Channel4_IRQHandler(void) {
    if(dma_interrupt_flag_get(DMA1, DMA_CH4, DMA_INT_FLAG_FTF)) {
        dma_interrupt_flag_clear(DMA1, DMA_CH4, DMA_INT_FLAG_FTF);

        // 快速设置标志，减少中断处理时间
        rx_flag = 1;

        // 立即重启DMA接收
        dma_channel_disable(DMA1, DMA_CH4);
        dma_transfer_number_config(DMA1, DMA_CH4, sizeof(uart_dma_buffer));
        dma_channel_enable(DMA1, DMA_CH4);
    }
}
```

## 10. 故障排除

### 10.1 常见问题及解决方案

**问题1：串口无输出**
- 检查硬件连接是否正确
- 验证波特率设置是否匹配
- 确认串口初始化是否成功

**问题2：接收数据丢失**
- 检查DMA配置是否正确
- 验证中断是否正常触发
- 确认接收缓冲区大小是否足够

**问题3：命令解析错误**
- 检查命令格式是否正确
- 验证字符串结束符是否正确
- 确认命令长度是否超出限制

### 10.2 调试工具
```c
// 串口自检函数
uint8_t usart_self_test(void) {
    my_printf(DEBUG_USART, "=== USART Self Test ===\r\n");

    // 测试发送功能
    my_printf(DEBUG_USART, "Testing TX... ");
    delay_ms(10);
    my_printf(DEBUG_USART, "OK\r\n");

    // 测试DMA配置
    if(dma_flag_get(DMA1, DMA_CH4, DMA_FLAG_FTF)) {
        my_printf(DEBUG_USART, "DMA Status: Ready\r\n");
    } else {
        my_printf(DEBUG_USART, "DMA Status: Busy\r\n");
    }

    // 测试中断配置
    if(nvic_irq_enable_get(DMA1_Channel4_IRQn)) {
        my_printf(DEBUG_USART, "Interrupt: Enabled\r\n");
    } else {
        my_printf(DEBUG_USART, "Interrupt: Disabled\r\n");
    }

    my_printf(DEBUG_USART, "==================\r\n");
    return 1;
}

// 显示详细的串口配置信息
void show_usart_config(void) {
    my_printf(DEBUG_USART, "=== USART Configuration ===\r\n");
    my_printf(DEBUG_USART, "Peripheral: USART0\r\n");
    my_printf(DEBUG_USART, "Baudrate: 115200\r\n");
    my_printf(DEBUG_USART, "Data bits: 8\r\n");
    my_printf(DEBUG_USART, "Stop bits: 1\r\n");
    my_printf(DEBUG_USART, "Parity: None\r\n");
    my_printf(DEBUG_USART, "Flow control: None\r\n");
    my_printf(DEBUG_USART, "DMA RX: Enabled\r\n");
    my_printf(DEBUG_USART, "Buffer size: %d bytes\r\n", sizeof(uart_dma_buffer));
    my_printf(DEBUG_USART, "========================\r\n");
}
```

## 11. 最佳实践

### 11.1 发送数据的最佳实践
```c
// 推荐：使用my_printf进行格式化输出
my_printf(DEBUG_USART, "Temperature: %d.%dC\r\n", temp/10, temp%10);

// 不推荐：直接使用底层函数
// usart_data_transmit(DEBUG_USART, 'T');
// usart_data_transmit(DEBUG_USART, 'e');
// ...
```

### 11.2 命令处理的最佳实践
```c
// 推荐：统一的命令处理流程
void handle_user_command(const char* cmd) {
    // 1. 记录命令到日志
    log_write_formatted("User command: %s", cmd);

    // 2. 添加到命令历史
    add_to_command_history(cmd);

    // 3. 处理命令
    process_command((char*)cmd);

    // 4. 输出提示符
    my_printf(DEBUG_USART, "> ");
}
```

### 11.3 错误处理的最佳实践
```c
// 推荐：详细的错误信息
void report_command_error(const char* cmd, uint8_t error_code) {
    my_printf(DEBUG_USART, "ERROR: Command '%s' failed\r\n", cmd);

    switch(error_code) {
        case CMD_ERROR_UNKNOWN:
            my_printf(DEBUG_USART, "Reason: Unknown command\r\n");
            my_printf(DEBUG_USART, "Type 'help' for available commands\r\n");
            break;
        case CMD_ERROR_INVALID_PARAM:
            my_printf(DEBUG_USART, "Reason: Invalid parameter\r\n");
            break;
        case CMD_ERROR_SYSTEM_BUSY:
            my_printf(DEBUG_USART, "Reason: System busy, try again later\r\n");
            break;
    }

    log_write_formatted("Command error: %s (code: %d)", cmd, error_code);
}
```

## 12. 扩展功能

### 12.1 多串口支持
```c
// 支持多个串口的通用发送函数
void multi_usart_printf(uint32_t usart_periph, const char *format, ...) {
    va_list args;
    char buffer[512];

    va_start(args, format);
    int len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    for(int i = 0; i < len; i++) {
        usart_data_transmit(usart_periph, buffer[i]);
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
    }
}

// 使用示例
multi_usart_printf(USART0, "Debug message\r\n");
multi_usart_printf(USART1, "Data: %d\r\n", sensor_data);
```

### 12.2 数据包协议支持
```c
// 简单的数据包格式：[STX][LEN][DATA][CHK][ETX]
#define STX 0x02
#define ETX 0x03

typedef struct {
    uint8_t stx;
    uint8_t length;
    uint8_t data[64];
    uint8_t checksum;
    uint8_t etx;
} data_packet_t;

// 发送数据包
void send_data_packet(const uint8_t* data, uint8_t len) {
    data_packet_t packet;

    packet.stx = STX;
    packet.length = len;
    memcpy(packet.data, data, len);

    // 计算校验和
    packet.checksum = 0;
    for(uint8_t i = 0; i < len; i++) {
        packet.checksum ^= data[i];
    }

    packet.etx = ETX;

    // 发送数据包
    uint8_t* packet_bytes = (uint8_t*)&packet;
    for(uint8_t i = 0; i < sizeof(data_packet_t); i++) {
        usart_data_transmit(DEBUG_USART, packet_bytes[i]);
        while(RESET == usart_flag_get(DEBUG_USART, USART_FLAG_TBE));
    }
}
```

这份串口模块使用指南提供了从基础初始化到高级功能的完整说明，包括所有函数的详细用法、调用位置和实际示例。接下来我将创建其他模块的文档。
```

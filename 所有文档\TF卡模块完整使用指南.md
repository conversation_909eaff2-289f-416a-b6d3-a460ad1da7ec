# TF卡模块使用指南

## 概述

本系统提供TF卡(MicroSD)基础存储功能，支持文件读写操作。TF卡主要用于存储日志文件和配置文件。

## 核心文件和函数位置

### 主要文件
- **sd_app.c** - TF卡应用层实现
- **bsp_sdio.c** - SDIO底层驱动
- **ff.c/ff.h** - FatFS文件系统
- **usart_app.c** - TF卡测试和配置文件处理
- **log_app.c** - 日志文件管理

## 1. TF卡初始化

### 1.1 文件系统挂载
```c
// 位置：sd_app.c
uint8_t init_filesystem(void)
```
**作用**：初始化SDIO硬件并挂载FAT文件系统

**调用位置**：
- main.c中的系统初始化
- usart_app.c中的TF卡测试
- log_app.c中的日志文件操作前

**调用示例**：
```c
if(init_filesystem()) {
    my_printf(DEBUG_USART, "TF card mounted successfully\r\n");
} else {
    my_printf(DEBUG_USART, "TF card mount failed\r\n");
}
```

### 1.2 TF卡检测
```c
// 位置：usart_app.c
uint8_t test_tf_card(void)
```
**作用**：测试TF卡是否正常工作并显示容量信息

**调用位置**：test命令处理中

## 2. 文件基础操作

### 2.1 文件打开
```c
// FatFS标准函数
FRESULT f_open(FIL* fp, const TCHAR* path, BYTE mode)
```
**作用**：打开或创建文件

**常用模式**：
- `FA_READ`: 只读模式
- `FA_WRITE`: 写入模式
- `FA_CREATE_ALWAYS`: 总是创建文件
- `FA_OPEN_ALWAYS`: 打开文件，不存在则创建

**调用示例**：
```c
FIL log_file;
FRESULT result;

// 创建新的日志文件
result = f_open(&log_file, "0:/log0.txt", FA_CREATE_ALWAYS | FA_WRITE);
if(result == FR_OK) {
    my_printf(DEBUG_USART, "File created successfully\r\n");
}

// 以追加模式打开现有文件
result = f_open(&log_file, "0:/data.txt", FA_OPEN_ALWAYS | FA_WRITE);
if(result == FR_OK) {
    f_lseek(&log_file, f_size(&log_file));  // 移动到文件末尾
}
```

### 2.2 文件写入
```c
// FatFS标准函数
FRESULT f_write(FIL* fp, const void* buff, UINT btw, UINT* bw)
```
**作用**：向文件写入数据

**调用示例**：
```c
char data[] = "Hello TF Card World!\r\n";
UINT bytes_written;

result = f_write(&log_file, data, strlen(data), &bytes_written);
if(result == FR_OK && bytes_written == strlen(data)) {
    my_printf(DEBUG_USART, "Data written successfully\r\n");
}

// 立即同步到TF卡
f_sync(&log_file);
```

### 2.3 文件读取
```c
// FatFS标准函数
FRESULT f_read(FIL* fp, void* buff, UINT btr, UINT* br)
```
**作用**：从文件读取数据

**调用示例**：
```c
char read_buffer[256];
UINT bytes_read;

result = f_read(&log_file, read_buffer, sizeof(read_buffer) - 1, &bytes_read);
if(result == FR_OK) {
    read_buffer[bytes_read] = '\0';  // 添加字符串结束符
    my_printf(DEBUG_USART, "Read data: %s\r\n", read_buffer);
}
```

### 2.4 文件关闭
```c
// FatFS标准函数
FRESULT f_close(FIL* fp)
```
**作用**：关闭文件并释放资源

**调用示例**：
```c
result = f_close(&log_file);
if(result == FR_OK) {
    my_printf(DEBUG_USART, "File closed successfully\r\n");
}
```

## 3. 日志文件管理

### 3.1 日志文件创建
```c
// 位置：log_app.c
uint8_t log_start_session(void)
```
**作用**：创建新的日志文件，文件名格式为`logN.txt`

**调用示例**：
```c
if(log_start_session()) {
    my_printf(DEBUG_USART, "Log session started\r\n");
}
```

### 3.2 日志内容写入
```c
// 位置：log_app.c
void log_write(const char* message)
void log_write_formatted(const char* format, ...)
```
**作用**：写入日志内容，自动添加时间戳

**调用示例**：
```c
// 写入简单消息
log_write("System started");

// 写入格式化消息
uint16_t voltage = 3300;
log_write_formatted("Voltage: %u.%03uV", voltage/1000, voltage%1000);
```

### 3.3 日志文件关闭
```c
// 位置：log_app.c
void log_close(void)
```
**作用**：关闭当前日志文件

## 4. 配置文件管理

### 4.1 配置文件读取
```c
// 位置：usart_app.c
uint8_t read_config_from_tf_card(void)
```
**作用**：从TF卡读取config.ini配置文件

**文件格式**：
```ini
[Ratio]
Ch0=1.50

[Limit]
Ch0=20.00
```

**调用示例**：
```c
if(read_config_from_tf_card()) {
    my_printf(DEBUG_USART, "Config loaded from TF card\r\n");
    my_printf(DEBUG_USART, "Ratio=%.2f\r\n", current_ratio/100.0f);
    my_printf(DEBUG_USART, "Limit=%.2f\r\n", current_limit/100.0f);
} else {
    my_printf(DEBUG_USART, "Config file not found, using defaults\r\n");
}
```

### 4.2 逐行文件读取
```c
// 位置：usart_app.c
uint8_t read_line_from_file(FIL* file, char* buffer, uint16_t buffer_size)
```
**作用**：从文件逐行读取内容

**调用示例**：
```c
FIL config_file;
char line_buffer[128];

if(f_open(&config_file, "0:/config.ini", FA_READ) == FR_OK) {
    while(read_line_from_file(&config_file, line_buffer, sizeof(line_buffer))) {
        // 处理每一行内容
        my_printf(DEBUG_USART, "Line: %s\r\n", line_buffer);

        // 解析配置项
        if(strstr(line_buffer, "Ch0=") != NULL) {
            char* value_start = strstr(line_buffer, "=");
            if(value_start != NULL) {
                float value = atof(value_start + 1);
                my_printf(DEBUG_USART, "Found value: %.2f\r\n", value);
            }
        }
    }
    f_close(&config_file);
}
```

## 5. 实际使用示例

### 5.1 完整的日志文件操作
```c
void example_log_operation(void) {
    // 1. 初始化文件系统
    if(!init_filesystem()) {
        my_printf(DEBUG_USART, "TF card init failed\r\n");
        return;
    }

    // 2. 创建日志会话
    if(!log_start_session()) {
        my_printf(DEBUG_USART, "Log session start failed\r\n");
        return;
    }

    // 3. 写入日志内容
    log_write("System started");
    log_write_formatted("Voltage: %u.%03uV", 3300/1000, 3300%1000);
    log_write("Test completed");

    // 4. 关闭日志文件
    log_close();

    my_printf(DEBUG_USART, "Log operation completed\r\n");
}
```

### 5.2 完整的配置文件操作
```c
void example_config_operation(void) {
    // 1. 初始化文件系统
    if(!init_filesystem()) {
        my_printf(DEBUG_USART, "TF card init failed\r\n");
        return;
    }

    // 2. 读取配置文件
    if(read_config_from_tf_card()) {
        my_printf(DEBUG_USART, "Config loaded successfully\r\n");
    } else {
        my_printf(DEBUG_USART, "Using default config\r\n");
    }

    // 3. 显示当前配置
    my_printf(DEBUG_USART, "Current Ratio: %.2f\r\n", current_ratio/100.0f);
    my_printf(DEBUG_USART, "Current Limit: %.2f\r\n", current_limit/100.0f);
}
```

### 5.3 简单的文件读写操作
```c
void example_file_readwrite(void) {
    FIL test_file;
    FRESULT result;
    UINT bytes_written, bytes_read;
    char write_data[] = "Hello TF Card!";
    char read_data[32];

    // 1. 初始化文件系统
    if(!init_filesystem()) {
        return;
    }

    // 2. 创建并写入文件
    result = f_open(&test_file, "0:/test.txt", FA_CREATE_ALWAYS | FA_WRITE);
    if(result == FR_OK) {
        f_write(&test_file, write_data, strlen(write_data), &bytes_written);
        f_sync(&test_file);
        f_close(&test_file);
        my_printf(DEBUG_USART, "File written: %s\r\n", write_data);
    }

    // 3. 读取文件内容
    result = f_open(&test_file, "0:/test.txt", FA_READ);
    if(result == FR_OK) {
        f_read(&test_file, read_data, sizeof(read_data)-1, &bytes_read);
        read_data[bytes_read] = '\0';
        f_close(&test_file);
        my_printf(DEBUG_USART, "File read: %s\r\n", read_data);
    }
}
```

## 6. 注意事项

### 6.1 文件操作流程
1. **初始化文件系统**：调用`init_filesystem()`
2. **打开文件**：使用`f_open()`
3. **读写操作**：使用`f_read()`或`f_write()`
4. **同步数据**：使用`f_sync()`确保数据写入
5. **关闭文件**：使用`f_close()`

### 6.2 错误处理
- 每个FatFS函数都返回`FRESULT`类型的结果
- `FR_OK`表示操作成功
- 其他值表示不同的错误类型
- 建议每次操作后都检查返回值

### 6.3 文件路径格式
- TF卡根目录：`"0:/"`
- 文件路径：`"0:/filename.txt"`
- 支持8.3文件名格式

### 6.4 性能建议
- 写入数据后及时调用`f_sync()`
- 不使用时及时关闭文件
- 避免频繁的小数据写入

### 6.5 常见问题
**问题1：TF卡无法挂载**
- 检查TF卡是否正确插入
- 确认TF卡格式为FAT32
- 检查TF卡是否损坏

**问题2：文件读写失败**
- 检查文件路径是否正确
- 确认TF卡有足够空间
- 验证文件是否被正确打开

**问题3：数据丢失**
- 确保调用了`f_sync()`
- 检查是否正确关闭文件
- 避免在写入过程中断电
```

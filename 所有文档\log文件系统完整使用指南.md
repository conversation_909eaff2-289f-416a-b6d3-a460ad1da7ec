# Log文件系统完整使用指南

## 概述

本系统提供了完整的日志文件管理功能，支持自动和手动两种模式。日志文件存储在TF卡上，文件名格式为`logN.txt`（N为数字编号）。

## 核心文件和函数位置

### 主要文件
- **log_app.c** - 日志系统核心实现
- **log_app.h** - 日志系统头文件
- **usart_app.c** - 串口和命令处理
- **main.c** - 系统初始化

## 1. 系统初始化

### 1.1 日志系统初始化
```c
// 位置：main.c
log_init();
```
**作用**：初始化日志系统，读取Flash中的配置信息，设置boot_count等参数

**内部调用流程**：
1. `read_log_config_from_flash()` - 从Flash读取日志配置
2. 如果配置无效，初始化默认配置
3. 设置全局变量`log_config`

### 1.2 文件系统初始化
```c
// 位置：sd_app.c
uint8_t init_filesystem(void)
```
**作用**：挂载TF卡文件系统，必须在使用日志功能前调用

## 2. 日志文件创建和打开

### 2.1 自动创建日志文件
```c
// 位置：log_app.c
uint8_t log_start_session(void)
```
**作用**：根据当前boot_count自动创建并打开日志文件

**调用示例**：
```c
if(log_start_session()) {
    // 文件创建成功
    my_printf(DEBUG_USART, "Log file opened successfully\r\n");
} else {
    // 文件创建失败
    my_printf(DEBUG_USART, "Failed to open log file\r\n");
}
```

**内部流程**：
1. 检查TF卡是否挂载
2. 根据`log_config.boot_count`生成文件名
3. 调用`f_open()`创建/打开文件
4. 写入会话开始标记

### 2.2 手动打开指定编号的日志文件
```c
// 位置：log_app.c
uint8_t open_log_file_by_number(uint32_t log_number)
```
**作用**：打开指定编号的日志文件

**调用示例**：
```c
// 打开log0.txt
if(open_log_file_by_number(0)) {
    my_printf(DEBUG_USART, "log0.txt opened successfully\r\n");
}

// 打开log1.txt
if(open_log_file_by_number(1)) {
    my_printf(DEBUG_USART, "log1.txt opened successfully\r\n");
}
```

**特殊处理**：
- 如果打开log0.txt，会自动写入pre_test_buffer中的内容
- 设置`current_open_log_number`跟踪当前打开的文件

## 3. 日志内容写入

### 3.1 基础写入函数
```c
// 位置：log_app.c
void log_write(const char* message)
```
**作用**：写入一行日志，自动添加时间戳

**调用示例**：
```c
log_write("System started");
log_write("Test completed successfully");
```

### 3.2 格式化写入函数
```c
// 位置：log_app.c
void log_write_formatted(const char* format, ...)
```
**作用**：支持printf格式的日志写入

**调用示例**：
```c
log_write_formatted("Voltage: %lu.%02luV", voltage/100, voltage%100);
log_write_formatted("Command: %s executed", cmd_name);
log_write_formatted("Error code: %d", error_code);
```

### 3.3 原始内容写入函数
```c
// 位置：log_app.c
void log_write_raw(const char* content)
```
**作用**：写入原始内容，不添加时间戳（用于pre_test_buffer）

**调用示例**：
```c
log_write_raw("2025-08-04 11:00:00 OUT: System ready");
```

## 4. 自动日志记录

### 4.1 串口输出自动记录
**位置**：usart_app.c的`my_printf()`函数

**机制**：
- 所有通过`my_printf()`输出的内容自动记录到日志
- 格式：`OUT: 内容`
- 根据`pre_test_logging`和`auto_log_enabled`状态决定记录位置

### 4.2 串口输入自动记录
**位置**：usart_app.c的`process_command()`函数

**机制**：
- 所有串口接收的命令自动记录到日志
- 格式：`IN: 命令`
- 同样根据系统状态决定记录位置

## 5. 日志文件关闭

### 5.1 自动关闭
```c
// 位置：log_app.c
void log_close(void)
```
**作用**：关闭当前打开的日志文件

**调用时机**：
- 系统关闭时
- 切换到其他日志文件时
- 手动关闭命令

### 5.2 手动关闭指定文件
```c
// 位置：log_app.c
uint8_t close_log_file_by_number(uint32_t log_number)
```
**作用**：关闭指定编号的日志文件

**调用示例**：
```c
// 关闭log0.txt
if(close_log_file_by_number(0)) {
    my_printf(DEBUG_USART, "log0.txt closed successfully\r\n");
}
```

**特殊处理**：
- 关闭log0.txt时会清空pre_test_buffer
- 写入关闭标记到文件
- 清除`current_open_log_number`

## 6. 状态查询函数

### 6.1 获取当前打开的日志编号
```c
// 位置：log_app.c
uint32_t get_current_log_number(void)
```
**作用**：返回当前打开的日志文件编号

### 6.2 检查日志系统是否初始化
```c
// 位置：log_app.c
uint8_t is_log_initialized(void)
```
**作用**：检查日志系统是否已经初始化

## 7. Boot计数管理

### 7.1 增加Boot计数
```c
// 位置：log_app.c
uint8_t increment_boot_count(void)
```
**作用**：增加boot计数，用于生成新的日志文件编号

### 7.2 设置Boot计数
```c
// 位置：log_app.c
uint8_t set_boot_count_and_save(uint32_t count)
```
**作用**：设置指定的boot计数值

### 7.3 重置日志系统
```c
// 位置：log_app.c
uint8_t reset_log_system(void)
```
**作用**：重置日志系统到未初始化状态

## 8. Pre-test Buffer系统

### 8.1 添加内容到Pre-test Buffer
```c
// 位置：usart_app.c
void add_to_pre_test_buffer(const char* message)
```
**作用**：在test成功前，将内容存储到Flash中的缓冲区

### 8.2 刷新Pre-test Buffer到日志
```c
// 位置：usart_app.c
void flush_pre_test_buffer_to_log(void)
```
**作用**：将pre_test_buffer中的内容写入到当前日志文件

### 8.3 清空Pre-test Buffer
```c
// 位置：usart_app.c
void clear_pre_test_buffer(void)
```
**作用**：清空pre_test_buffer，重置为初始状态

## 9. 完整使用流程示例

### 9.1 系统启动时的日志初始化
```c
// main.c中的初始化序列
void main(void) {
    // ... 硬件初始化 ...
    
    // 初始化日志系统
    log_init();
    
    // ... 其他初始化 ...
}
```

### 9.2 Test命令的日志处理流程
```c
// usart_app.c中的test命令处理
if(strcmp(cmd_str, "test") == 0) {
    // 开始测试
    my_printf(DEBUG_USART, "=====system selftest=====\r\n");
    
    // 测试Flash和TF卡
    if(test_tf_card()) {
        // 测试成功，创建log0.txt
        set_boot_count_and_save(0);
        
        if(!is_log_initialized()) {
            set_log_initialized();
            
            // 创建log0.txt并写入pre_test_buffer内容
            log_start_session();
            flush_pre_test_buffer_to_log();
            
            // 写入测试成功信息
            log_write("Test successful - System first initialization");
            log_write("Flash detection completed");
            log_write("TF card detection completed");
            log_write("=== log0.txt completed ===");
            
            // 关闭log0.txt
            log_close();
            
            // 创建log1.txt用于后续操作
            increment_boot_count();
            log_start_session();
        }
    }
}
```

### 9.3 手动日志文件操作
```c
// 手动打开log文件
if(strcmp(cmd_str, "log0 open") == 0) {
    if(open_log_file_by_number(0)) {
        // 成功打开
    }
}

// 手动关闭log文件
if(strcmp(cmd_str, "log0 close") == 0) {
    if(close_log_file_by_number(0)) {
        // 成功关闭
    }
}
```

## 10. 注意事项

### 10.1 文件系统依赖
- 所有日志操作前必须确保TF卡已正确挂载
- 使用`init_filesystem()`检查文件系统状态

### 10.2 内存管理
- Pre-test buffer大小限制为1024字节
- 超出限制的内容会被丢弃

### 10.3 Flash存储
- 日志配置和pre_test_buffer都存储在Flash中
- 断电后数据不会丢失

### 10.4 线程安全
- 当前实现不支持多线程并发访问
- 在中断中调用日志函数需要注意

## 11. 错误处理

### 11.1 常见错误码
- 文件打开失败：检查TF卡是否插入
- 写入失败：检查TF卡空间是否足够
- 初始化失败：检查Flash是否正常

### 11.2 调试方法
- 使用串口输出查看详细错误信息
- 检查`current_open_log_number`状态
- 验证文件系统挂载状态

## 12. 实际应用场景

### 12.1 系统启动日志记录
```c
// 在系统各个初始化阶段记录日志
void system_init_with_logging(void) {
    log_write("=== System Boot Start ===");

    // 硬件初始化
    bsp_led_init();
    log_write("LED initialized");

    bsp_usart_init();
    log_write("USART initialized");

    bsp_rtc_init();
    log_write("RTC initialized");

    // ADC初始化
    bsp_adc_init();
    log_write("ADC initialized");

    log_write("=== System Boot Complete ===");
}
```

### 12.2 数据采样日志记录
```c
// 在采样任务中记录数据
void sampling_with_logging(void) {
    // 开始采样
    log_write("Sampling started");

    // 记录采样数据
    uint16_t voltage_mv = get_adc_voltage();
    log_write_formatted("Sample: %u.%02uV", voltage_mv/1000, (voltage_mv%1000)/10);

    // 检查是否超限
    if(voltage_mv > limit_value) {
        log_write_formatted("ALERT: Voltage over limit (%u.%02uV > %u.%02uV)",
                           voltage_mv/1000, (voltage_mv%1000)/10,
                           limit_value/1000, (limit_value%1000)/10);
    }
}
```

### 12.3 错误和异常日志记录
```c
// 错误处理中的日志记录
void error_handling_with_logging(uint8_t error_code) {
    switch(error_code) {
        case ERROR_TF_CARD:
            log_write("ERROR: TF card not detected");
            break;
        case ERROR_FLASH:
            log_write("ERROR: Flash communication failed");
            break;
        case ERROR_ADC:
            log_write("ERROR: ADC conversion timeout");
            break;
        default:
            log_write_formatted("ERROR: Unknown error code %d", error_code);
            break;
    }
}
```

### 12.4 配置变更日志记录
```c
// 配置修改时的日志记录
void config_change_logging(void) {
    // 记录ratio修改
    if(process_ratio_input(input_str)) {
        log_write_formatted("Config: Ratio changed to %u.%02u",
                           current_ratio/100, current_ratio%100);
    } else {
        log_write_formatted("Config: Invalid ratio input '%s'", input_str);
    }

    // 记录limit修改
    if(process_limit_input(input_str)) {
        log_write_formatted("Config: Limit changed to %u.%02u",
                           current_limit/100, current_limit%100);
    } else {
        log_write_formatted("Config: Invalid limit input '%s'", input_str);
    }
}
```

## 13. 高级功能

### 13.1 条件日志记录
```c
// 根据条件决定是否记录日志
void conditional_logging(uint8_t log_level, const char* message) {
    static uint8_t current_log_level = LOG_LEVEL_INFO;

    if(log_level >= current_log_level) {
        switch(log_level) {
            case LOG_LEVEL_ERROR:
                log_write_formatted("ERROR: %s", message);
                break;
            case LOG_LEVEL_WARNING:
                log_write_formatted("WARNING: %s", message);
                break;
            case LOG_LEVEL_INFO:
                log_write_formatted("INFO: %s", message);
                break;
            case LOG_LEVEL_DEBUG:
                log_write_formatted("DEBUG: %s", message);
                break;
        }
    }
}
```

### 13.2 批量日志写入
```c
// 批量写入多条日志
void batch_log_write(const char* messages[], uint8_t count) {
    log_write("=== Batch Log Start ===");

    for(uint8_t i = 0; i < count; i++) {
        log_write(messages[i]);
    }

    log_write("=== Batch Log End ===");
}
```

### 13.3 日志文件轮转
```c
// 当日志文件过大时创建新文件
uint8_t rotate_log_file_if_needed(void) {
    // 检查当前文件大小
    if(f_size(&log_file) > MAX_LOG_FILE_SIZE) {
        log_write("=== Log file size limit reached, rotating ===");

        // 关闭当前文件
        log_close();

        // 增加boot计数，创建新文件
        increment_boot_count();

        // 打开新文件
        if(log_start_session()) {
            log_write("=== New log file created ===");
            return 1;
        }
    }
    return 0;
}
```

## 14. 性能优化建议

### 14.1 减少Flash写入频率
```c
// 使用缓冲区减少Flash写入次数
static char log_buffer[512];
static uint16_t buffer_pos = 0;

void buffered_log_write(const char* message) {
    uint16_t msg_len = strlen(message);

    if(buffer_pos + msg_len < sizeof(log_buffer)) {
        strcpy(&log_buffer[buffer_pos], message);
        buffer_pos += msg_len;
    } else {
        // 缓冲区满，写入文件
        flush_log_buffer();
        strcpy(log_buffer, message);
        buffer_pos = msg_len;
    }
}

void flush_log_buffer(void) {
    if(buffer_pos > 0) {
        f_write(&log_file, log_buffer, buffer_pos, &bytes_written);
        f_sync(&log_file);
        buffer_pos = 0;
    }
}
```

### 14.2 异步日志写入
```c
// 使用队列实现异步日志写入
typedef struct {
    char message[128];
    uint32_t timestamp;
} log_entry_t;

static log_entry_t log_queue[LOG_QUEUE_SIZE];
static uint8_t queue_head = 0;
static uint8_t queue_tail = 0;

void async_log_write(const char* message) {
    // 添加到队列
    strcpy(log_queue[queue_tail].message, message);
    log_queue[queue_tail].timestamp = rtc_to_unix_timestamp();

    queue_tail = (queue_tail + 1) % LOG_QUEUE_SIZE;

    // 如果队列满，覆盖最老的条目
    if(queue_tail == queue_head) {
        queue_head = (queue_head + 1) % LOG_QUEUE_SIZE;
    }
}

void process_log_queue(void) {
    while(queue_head != queue_tail) {
        log_entry_t* entry = &log_queue[queue_head];

        // 格式化并写入日志
        char formatted_msg[256];
        format_timestamp_message(entry->timestamp, entry->message, formatted_msg);
        log_write_raw(formatted_msg);

        queue_head = (queue_head + 1) % LOG_QUEUE_SIZE;
    }
}
```

## 15. 故障排除指南

### 15.1 常见问题及解决方案

**问题1：日志文件无法创建**
- 检查TF卡是否正确插入
- 验证文件系统是否正确挂载
- 确认TF卡有足够的剩余空间

**问题2：日志内容丢失**
- 检查`f_sync()`是否正确调用
- 验证Flash写入是否成功
- 确认pre_test_buffer是否正确保存

**问题3：日志文件编号混乱**
- 检查boot_count是否正确保存到Flash
- 验证`current_open_log_number`状态
- 确认日志系统初始化是否正确

**问题4：性能问题**
- 减少频繁的Flash写入操作
- 使用缓冲区批量写入
- 避免在中断中进行复杂的日志操作

### 15.2 调试工具函数
```c
// 打印日志系统状态
void debug_log_system_status(void) {
    my_printf(DEBUG_USART, "=== Log System Status ===\r\n");
    my_printf(DEBUG_USART, "Boot count: %lu\r\n", log_config.boot_count);
    my_printf(DEBUG_USART, "Initialized: %s\r\n", log_config.initialized ? "Yes" : "No");
    my_printf(DEBUG_USART, "Current open log: %lu\r\n", get_current_log_number());
    my_printf(DEBUG_USART, "Pre-test logging: %s\r\n", pre_test_logging ? "Enabled" : "Disabled");
    my_printf(DEBUG_USART, "Auto log enabled: %s\r\n", auto_log_enabled ? "Yes" : "No");
    my_printf(DEBUG_USART, "Pre-test buffer pos: %u\r\n", pre_test_buffer_pos);
}

// 验证文件系统状态
void debug_filesystem_status(void) {
    my_printf(DEBUG_USART, "=== Filesystem Status ===\r\n");

    if(init_filesystem()) {
        my_printf(DEBUG_USART, "Filesystem: Mounted\r\n");

        DWORD fre_clust, tot_sect, fre_sect;
        FATFS* fs_ptr = NULL;

        if(f_getfree("0:", &fre_clust, &fs_ptr) == FR_OK) {
            tot_sect = (fs_ptr->n_fatent - 2) * fs_ptr->csize;
            fre_sect = fre_clust * fs_ptr->csize;

            my_printf(DEBUG_USART, "Total space: %lu KB\r\n", tot_sect / 2);
            my_printf(DEBUG_USART, "Free space: %lu KB\r\n", fre_sect / 2);
        }
    } else {
        my_printf(DEBUG_USART, "Filesystem: Not mounted\r\n");
    }
}
```

这份文档提供了完整的log文件系统使用指南，包括所有函数的详细说明、调用位置、使用示例和最佳实践。你可以根据这份文档来正确使用日志系统的各项功能。
